# 🚀 Lahza Payment Gateway - Complete Payment Testing Suite

## Overview

This comprehensive testing suite provides everything you need to simulate, test, and validate end-to-end payment processes with the Lahza payment gateway in WHMCS using PowerShell.

---

## 📁 Files Included

### Core Simulation Scripts
- **`simulate-payment.ps1`** - Main end-to-end payment simulation script
- **`run-payment-simulation.bat`** - Easy-to-use batch launcher
- **`example-usage.ps1`** - Comprehensive usage examples

### Documentation
- **`PAYMENT_SIMULATION_GUIDE.md`** - Complete user guide
- **`COMPLETE_PAYMENT_TESTING_SUITE.md`** - This overview document

### Existing Diagnostic Tools
- **`quick-check.ps1`** - Fast connectivity and basic checks
- **`diagnose.ps1`** - Comprehensive diagnostic tool
- **`test-webhook.ps1`** - Webhook-specific testing

---

## 🎯 What This Suite Accomplishes

### Complete Payment Flow Simulation
1. **API Connectivity Testing** - Verifies connection to Lahza API endpoints
2. **Invoice Management** - Creates or validates existing invoices
3. **Payment Processing** - Simulates card payments with test credentials
4. **Webhook Handling** - Tests callback processing and signature verification
5. **Status Verification** - Confirms invoice updates and payment completion

### Test Card Integration
- **Visa Test Card**: ************** 1111
- **Expiry**: 03/30
- **CVV**: 004
- **Safe Testing**: Never charges real money

### Comprehensive Logging
- Step-by-step process tracking
- API request/response logging
- Error handling and troubleshooting
- Success/failure indicators

---

## 🚀 Quick Start (3 Steps)

### Step 1: Navigate to Directory
```cmd
cd "C:\xampp\htdocs\Whmcs\modules\gateways\lahza"
```

### Step 2: Choose Your Method

**Option A: Use Batch Launcher (Easiest)**
```cmd
run-payment-simulation.bat
```

**Option B: Direct PowerShell (Advanced)**
```powershell
.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 25.00
```

### Step 3: Review Results
- Check console output for success/failure indicators
- Verify invoice status in WHMCS admin
- Review gateway logs for transaction details

---

## 📋 Testing Scenarios Covered

### Scenario 1: New Invoice Creation and Payment
```powershell
.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 50.00 -Currency "USD"
```
**Tests:** Invoice creation, payment processing, webhook handling

### Scenario 2: Existing Invoice Payment
```powershell
.\simulate-payment.ps1 -Domain "localhost" -InvoiceId "123"
```
**Tests:** Invoice validation, payment simulation, status updates

### Scenario 3: Production-like Testing with Secret Key
```powershell
.\simulate-payment.ps1 -Domain "localhost" -InvoiceId "123" -SecretKey "your_test_key"
```
**Tests:** API authentication, webhook signing, signature verification

### Scenario 4: Multi-Currency Support
```powershell
# USD
.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 25.00 -Currency "USD"

# Israeli Shekel
.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 90.00 -Currency "ILS"

# Jordanian Dinar
.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 18.00 -Currency "JOD"
```
**Tests:** Currency handling, amount conversion, localization

### Scenario 5: Error Handling and Recovery
```powershell
.\simulate-payment.ps1 -Domain "localhost" -InvoiceId "999999" -Verbose
```
**Tests:** Invalid invoice handling, error reporting, graceful failures

---

## 🔍 Validation Checklist

After running simulations, verify these success criteria:

### ✅ Technical Validation
- [ ] All 5 simulation steps completed successfully
- [ ] HTTP 200 responses from webhook calls
- [ ] No PowerShell errors or exceptions
- [ ] API connectivity tests passed

### ✅ WHMCS Integration Validation
- [ ] Invoice status changed to "Paid"
- [ ] Transaction logged in gateway logs
- [ ] Correct amount and currency recorded
- [ ] Client notifications sent (if configured)

### ✅ Lahza Gateway Validation
- [ ] Payment reference generated correctly
- [ ] Webhook payload structured properly
- [ ] HMAC signature verification (if using secret key)
- [ ] Card details handled securely

---

## 🛠️ Troubleshooting Guide

### Common Issues and Solutions

#### Issue: "PowerShell execution policy"
**Error:** `cannot be loaded because running scripts is disabled`
**Solution:**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### Issue: "DNS resolution failed"
**Error:** `Cannot resolve hostname: api.lahza.io`
**Solution:**
```cmd
# Test basic connectivity
ping api.lahza.io
nslookup api.lahza.io *******

# Check firewall/antivirus settings
```

#### Issue: "Invoice not found"
**Error:** `Invoice not found or error`
**Solution:**
```powershell
# Create new invoice instead
.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 15.00

# Or check WHMCS for valid invoice IDs
```

#### Issue: "Webhook failed"
**Error:** `Webhook failed: HTTP 500`
**Solution:**
```powershell
# Run diagnostic first
.\diagnose.ps1 -Domain "localhost" -TestWebhook

# Check WHMCS error logs
# Verify gateway configuration
```

---

## 📊 Expected Output Examples

### Successful Simulation Output
```
🚀 LAHZA PAYMENT SIMULATION
========================================

🔍 STEP 0: API CONNECTIVITY TEST
---------------------------------
✅ DNS resolution successful: *************
✅ Lahza API accessible: HTTP 200
✅ API endpoint test successful: HTTP 200

📋 STEP 1: INVOICE PREPARATION
------------------------------
✅ Test invoice created: ID 156

🚀 STEP 2: PAYMENT INITIALIZATION
---------------------------------
✅ Payment page accessible
✅ Lahza gateway detected on payment page
ℹ️ Payment Reference: INV-156-20250617143022-7834

💳 STEP 3: PAYMENT PROCESSING
-----------------------------
✅ Payment authorized successfully

🔔 STEP 4: WEBHOOK NOTIFICATION
-------------------------------
✅ Webhook sent successfully: HTTP 200

✅ STEP 5: PAYMENT VERIFICATION
-------------------------------
✅ PAYMENT SUCCESSFUL! Invoice 156 is now marked as PAID

📊 SIMULATION SUMMARY
=====================
Invoice ID: 156
Amount: 25.00 USD
Payment Reference: INV-156-20250617143022-7834
Webhook Status: ✅ Success (HTTP 200)
Final Invoice Status: Paid

✅ Payment simulation completed successfully!
```

---

## 🔗 Integration with Existing Tools

### Use with Diagnostic Suite
```powershell
# Complete testing workflow
.\quick-check.ps1 localhost                    # Basic checks
.\diagnose.ps1 -Domain "localhost"             # Full diagnostic
.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice  # Payment test
.\test-webhook.ps1 -Domain "localhost" -InvoiceId "123"    # Webhook verify
```

### WHMCS Admin Integration
- **Invoices**: `https://localhost/admin/invoices.php`
- **Gateway Logs**: `https://localhost/admin/logs.php?type=gateway`
- **Transactions**: `https://localhost/admin/transactions.php`

### Web-Based Tools
- **Debug Dashboard**: `https://localhost/modules/gateways/lahza/tools.html`
- **Webhook Debugger**: `https://localhost/modules/gateways/lahza/debug_webhook.php`
- **Log Viewer**: `https://localhost/modules/gateways/lahza/view_logs.php`

---

## 🎯 Best Practices

### Before Testing
1. ✅ Ensure XAMPP/WAMP is running
2. ✅ Verify WHMCS is accessible
3. ✅ Confirm Lahza gateway is activated
4. ✅ Have test invoice IDs ready (optional)

### During Testing
1. 🔄 Start with basic scenarios first
2. 🔄 Use verbose mode for troubleshooting
3. 🔄 Test different currencies and amounts
4. 🔄 Verify results in WHMCS admin

### After Testing
1. 📊 Review all console output
2. 📊 Check WHMCS gateway logs
3. 📊 Verify invoice status changes
4. 📊 Document any issues found

---

## 📞 Support and Documentation

### Self-Help Resources
- **Complete Guide**: `PAYMENT_SIMULATION_GUIDE.md`
- **Usage Examples**: `example-usage.ps1`
- **Diagnostic Tools**: `POWERSHELL_DIAGNOSTIC.md`

### When Reporting Issues
Include this information:
- Complete PowerShell output
- WHMCS version and configuration
- Domain/server setup details
- Any error messages from logs

---

## 🎉 Success Metrics

A fully successful test suite run should achieve:
- **100% Step Completion** - All 5 simulation steps pass
- **Invoice Status Update** - Status changes to "Paid"
- **Gateway Log Entries** - Transactions recorded properly
- **No Critical Errors** - Clean execution without failures

**🎯 Goal:** Achieve consistent, reliable payment processing simulation that mirrors real-world Lahza payment flows!

---

**💡 Pro Tip:** Run the complete suite regularly during development to catch integration issues early!

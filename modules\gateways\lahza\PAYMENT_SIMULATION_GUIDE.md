# 🚀 Lahza Payment Gateway - Complete Payment Simulation Guide

## Overview

This guide provides comprehensive instructions for simulating end-to-end payment processes using the Lahza payment gateway in WHMCS through PowerShell.

---

## 🎯 What This Simulation Does

The `simulate-payment.ps1` script performs a complete payment flow simulation:

1. **API Connectivity Test** - Verifies connection to Lahza API
2. **Invoice Preparation** - Creates or validates an existing invoice
3. **Payment Initialization** - Simulates payment gateway initialization
4. **Payment Processing** - Simulates card payment with test credentials
5. **Webhook Notification** - Sends payment success webhook to WHMCS
6. **Payment Verification** - Confirms invoice status update

---

## 🛠️ Prerequisites

### System Requirements
- **Windows PowerShell 5.1+** or **PowerShell Core 6.0+**
- **XAMPP/WAMP** or similar local development environment
- **WHMCS 8.0+** with Lahza gateway installed
- **Internet connection** for API testing

### WHMCS Configuration
- Lahza payment gateway activated in WHMCS
- Valid test API keys configured (optional for simulation)
- At least one client account in WHMCS

---

## 🚀 Quick Start

### Basic Simulation (Recommended)
```powershell
# Navigate to the Lahza gateway directory
cd "C:\xampp\htdocs\Whmcs\modules\gateways\lahza"

# Run basic simulation with existing invoice
.\simulate-payment.ps1 -Domain "localhost" -InvoiceId "123"

# Run simulation with secret key for webhook signing
.\simulate-payment.ps1 -Domain "localhost" -InvoiceId "123" -SecretKey "your_test_secret_key"
```

### Create New Invoice and Test
```powershell
# Create new test invoice and simulate payment
.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 25.00

# Create invoice with specific currency
.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 50.00 -Currency "USD"
```

### Verbose Testing
```powershell
# Run with detailed output for troubleshooting
.\simulate-payment.ps1 -Domain "localhost" -InvoiceId "123" -Verbose
```

---

## 📋 Command Parameters

| Parameter | Required | Default | Description |
|-----------|----------|---------|-------------|
| `-Domain` | ✅ Yes | - | Your WHMCS domain (e.g., "localhost", "yourdomain.com") |
| `-InvoiceId` | ❌ No | "" | Existing invoice ID to test with |
| `-SecretKey` | ❌ No | "" | Lahza secret key for webhook signature |
| `-Amount` | ❌ No | 10.00 | Payment amount for new invoices |
| `-Currency` | ❌ No | "USD" | Currency code (USD, ILS, JOD) |
| `-CreateInvoice` | ❌ No | false | Create new test invoice |
| `-Verbose` | ❌ No | false | Show detailed output |

---

## 💳 Test Card Information

The simulation uses these **safe test card credentials**:

```
Card Type: Visa
Card Number: 4111 1111 1111 1111
Expiry Date: 03/30
CVV: 004
```

**Note:** These are standard test card numbers that will never charge real money.

---

## 📊 Understanding Results

### ✅ Success Indicators
- **Green checkmarks (✅)** - Steps completed successfully
- **"Payment simulation completed successfully!"** - Full success
- **Invoice status changes to "Paid"** - Payment processed correctly

### ⚠️ Warning Indicators
- **Yellow warnings (⚠️)** - Non-critical issues that don't stop the process
- **"API connectivity issues detected"** - Network or API problems
- **"Invoice already paid"** - Testing with already-paid invoice

### ❌ Error Indicators
- **Red errors (❌)** - Critical failures that stop the simulation
- **"Invoice not found"** - Invalid invoice ID
- **"Webhook failed"** - Callback processing error

---

## 🔍 Step-by-Step Process Explanation

### Step 0: API Connectivity Test
```
🔍 STEP 0: API CONNECTIVITY TEST
---------------------------------
✅ DNS resolution successful: 161.35.20.140
✅ Lahza API accessible: HTTP 200
✅ API endpoint test successful: HTTP 200
```

**What it does:**
- Tests DNS resolution for `api.lahza.io`
- Verifies HTTPS connectivity to Lahza API
- Tests API endpoint with provided credentials (if available)

### Step 1: Invoice Preparation
```
📋 STEP 1: INVOICE PREPARATION
------------------------------
✅ Invoice found: Status = Unpaid, Amount = 10.00 USD
```

**What it does:**
- Creates new test invoice (if `-CreateInvoice` specified)
- Validates existing invoice (if `-InvoiceId` provided)
- Checks current invoice status and amount

### Step 2: Payment Initialization
```
🚀 STEP 2: PAYMENT INITIALIZATION
---------------------------------
✅ Payment page accessible
✅ Lahza gateway detected on payment page
ℹ️ Payment Reference: INV-123-20250617143022-7834
ℹ️ Test Card: Visa ending in 1111
ℹ️ Expiry: 03/30, CVV: 004
```

**What it does:**
- Tests payment page accessibility
- Generates unique payment reference
- Prepares payment data with test card information

### Step 3: Payment Processing
```
💳 STEP 3: PAYMENT PROCESSING
-----------------------------
✅ Payment authorized successfully
ℹ️ Transaction Reference: INV-123-20250617143022-7834
```

**What it does:**
- Simulates payment processing delay
- Generates transaction reference
- Simulates successful authorization

### Step 4: Webhook Notification
```
🔔 STEP 4: WEBHOOK NOTIFICATION
-------------------------------
✅ Webhook sent successfully: HTTP 200
```

**What it does:**
- Creates realistic webhook payload
- Signs payload with HMAC-SHA256 (if secret key provided)
- Sends POST request to WHMCS callback handler
- Includes complete transaction and card details

### Step 5: Payment Verification
```
✅ STEP 5: PAYMENT VERIFICATION
-------------------------------
✅ PAYMENT SUCCESSFUL! Invoice 123 is now marked as PAID
```

**What it does:**
- Queries WHMCS API for updated invoice status
- Confirms payment was processed correctly
- Reports final invoice state

---

## 🔧 Troubleshooting Common Issues

### Issue: "DNS resolution failed"
**Cause:** Network connectivity or DNS server issues
**Solution:**
```powershell
# Test basic connectivity
ping api.lahza.io
nslookup api.lahza.io

# Try with different DNS
.\simulate-payment.ps1 -Domain "localhost" -InvoiceId "123" -Verbose
```

### Issue: "Invoice not found"
**Cause:** Invalid invoice ID or WHMCS API issues
**Solution:**
```powershell
# Create new test invoice instead
.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 15.00

# Check WHMCS admin area for valid invoice IDs
```

### Issue: "Webhook failed"
**Cause:** WHMCS callback handler issues or server configuration
**Solution:**
```powershell
# Test webhook URL directly
curl -X POST https://localhost/modules/gateways/callback/lahza.php

# Check WHMCS error logs
# Review server error logs (Apache/Nginx)
```

### Issue: "Payment page not accessible"
**Cause:** WHMCS not running or incorrect domain
**Solution:**
```powershell
# Verify WHMCS is running
curl https://localhost/admin/

# Check domain parameter
.\simulate-payment.ps1 -Domain "127.0.0.1" -InvoiceId "123"
```

---

## 📈 Advanced Usage Examples

### Batch Testing Multiple Invoices
```powershell
$invoices = @("10", "11", "12", "13", "14")
foreach ($invoice in $invoices) {
    Write-Host "Testing invoice $invoice..." -ForegroundColor Cyan
    .\simulate-payment.ps1 -Domain "localhost" -InvoiceId $invoice -SecretKey "your_key"
    Start-Sleep -Seconds 3
    Write-Host ""
}
```

### Testing Different Amounts and Currencies
```powershell
# Test USD payment
.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 25.00 -Currency "USD"

# Test ILS payment
.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 100.00 -Currency "ILS"

# Test JOD payment
.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 15.00 -Currency "JOD"
```

### Automated Testing Loop
```powershell
# Run continuous testing every 5 minutes
while ($true) {
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] Running payment simulation..." -ForegroundColor Blue
    
    .\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount (Get-Random -Minimum 10 -Maximum 100)
    
    Write-Host "Waiting 5 minutes for next test..." -ForegroundColor Gray
    Start-Sleep -Seconds 300
}
```

---

## 🔗 Integration with Other Tools

### Use with Existing Diagnostic Tools
```powershell
# Run full diagnostic first
.\diagnose.ps1 -Domain "localhost" -TestWebhook

# Then run payment simulation
.\simulate-payment.ps1 -Domain "localhost" -InvoiceId "123"

# Check results with webhook test
.\test-webhook.ps1 -Domain "localhost" -InvoiceId "123"
```

### Web-Based Verification
After running the simulation, verify results using:
- **WHMCS Admin**: `https://localhost/admin/invoices.php`
- **Gateway Logs**: `https://localhost/admin/logs.php?type=gateway`
- **Debug Tools**: `https://localhost/modules/gateways/lahza/tools.html`

---

## 📞 Getting Help

### Self-Diagnosis Checklist
1. ✅ XAMPP/WAMP running correctly
2. ✅ WHMCS accessible via browser
3. ✅ Lahza gateway activated in WHMCS
4. ✅ Internet connection working
5. ✅ PowerShell execution policy allows scripts

### Contact Information
When reporting issues, include:
- Complete PowerShell output
- WHMCS version
- Domain/server configuration
- Any error messages from logs

---

## 🎉 Success Criteria

A successful simulation should show:
- ✅ All 5 steps completed without errors
- ✅ Invoice status changed to "Paid"
- ✅ Transaction logged in WHMCS gateway logs
- ✅ Webhook received and processed correctly

**🎯 Pro Tip:** Run the simulation multiple times with different invoices to ensure consistent behavior!

# Lahza Payment Gateway - Complete End-to-End Test
# Creates new client, invoice, processes payment, and verifies everything
# Usage: .\complete-end-to-end-test.ps1 -Domain "localhost/Whmcs" -SecretKey "your_secret"

param(
    [Parameter(Mandatory=$false)]
    [string]$Domain = "localhost/Whmcs",
    
    [Parameter(Mandatory=$false)]
    [string]$SecretKey = "sk_test_kAFTeibRx3HS66tP0MfeBYqAviilBb7im",
    
    [Parameter(Mandatory=$false)]
    [decimal]$Amount = 25.00,
    
    [Parameter(Mandatory=$false)]
    [string]$Currency = "USD",
    
    [Parameter(Mandatory=$false)]
    [string]$AdminUsername = "admin",
    
    [Parameter(Mandatory=$false)]
    [string]$AdminPassword = "admin123"
)

# Test card data
$TestCard = @{
    card_number = "****************"
    exp_month   = "03"
    exp_year    = "30"
    cvv         = "004"
    card_type   = "Visa"
}

# Test customer data
$TestCustomer = @{
    firstname = "Ahmed"
    lastname = "Al-<PERSON><PERSON><PERSON>"
    email = "ahmed.test.$(Get-Date -Format 'yyyyMMddHHmmss')@example.com"
    address1 = "123 Palestine Street"
    address2 = "Apartment 4B"
    city = "Ramallah"
    state = "West Bank"
    postcode = "P400"
    country = "PS"
    phonenumber = "+************"
    companyname = "Test Company Ltd"
    currency = $Currency
}

# Color functions
function Write-Success { param([string]$Message) Write-Host "SUCCESS: $Message" -ForegroundColor Green }
function Write-Error { param([string]$Message) Write-Host "ERROR: $Message" -ForegroundColor Red }
function Write-Warning { param([string]$Message) Write-Host "WARNING: $Message" -ForegroundColor Yellow }
function Write-Info { param([string]$Message) Write-Host "INFO: $Message" -ForegroundColor Cyan }
function Write-Step { param([string]$Message) Write-Host "STEP: $Message" -ForegroundColor Blue }

# Function to generate HMAC signature
function Get-HMACSignature {
    param([string]$Data, [string]$Key)
    
    if ([string]::IsNullOrEmpty($Key)) {
        return "test_signature_no_key_provided"
    }
    
    $hmac = New-Object System.Security.Cryptography.HMACSHA256
    $hmac.Key = [System.Text.Encoding]::UTF8.GetBytes($Key)
    $hash = $hmac.ComputeHash([System.Text.Encoding]::UTF8.GetBytes($Data))
    return [System.BitConverter]::ToString($hash).Replace("-", "").ToLower()
}

# Function to create new client
function New-TestClient {
    param([string]$Domain, [hashtable]$CustomerData, [string]$AdminUsername, [string]$AdminPassword)
    
    Write-Step "Creating new test client..."
    
    $apiUrl = "http://$Domain/includes/api.php"
    $clientData = @{
        action = "AddClient"
        firstname = $CustomerData.firstname
        lastname = $CustomerData.lastname
        email = $CustomerData.email
        address1 = $CustomerData.address1
        address2 = $CustomerData.address2
        city = $CustomerData.city
        state = $CustomerData.state
        postcode = $CustomerData.postcode
        country = $CustomerData.country
        phonenumber = $CustomerData.phonenumber
        companyname = $CustomerData.companyname
        currency = $CustomerData.currency
        password2 = "TestPassword123!"
        username = $AdminUsername
        password = $AdminPassword
        responsetype = "json"
    }
    
    try {
        $response = Invoke-RestMethod -Uri $apiUrl -Method Post -Body $clientData -ContentType "application/x-www-form-urlencoded"
        
        if ($response.result -eq "success") {
            Write-Success "Client created successfully: ID $($response.clientid)"
            Write-Info "Client Details:"
            Write-Host "   Name: $($CustomerData.firstname) $($CustomerData.lastname)" -ForegroundColor White
            Write-Host "   Email: $($CustomerData.email)" -ForegroundColor White
            Write-Host "   Phone: $($CustomerData.phonenumber)" -ForegroundColor White
            Write-Host "   Address: $($CustomerData.address1), $($CustomerData.city), $($CustomerData.country)" -ForegroundColor White
            Write-Host "   Company: $($CustomerData.companyname)" -ForegroundColor White
            
            return @{
                Success = $true
                ClientId = $response.clientid
                ClientData = $CustomerData
            }
        } else {
            Write-Error "Failed to create client: $($response.message)"
            return @{ Success = $false; Error = $response.message }
        }
    }
    catch {
        Write-Error "API call failed: $($_.Exception.Message)"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Function to create new invoice
function New-TestInvoice {
    param([string]$Domain, [string]$ClientId, [decimal]$Amount, [string]$Currency, [string]$AdminUsername, [string]$AdminPassword)
    
    Write-Step "Creating new test invoice..."
    
    $apiUrl = "http://$Domain/includes/api.php"
    $invoiceData = @{
        action = "CreateInvoice"
        userid = $ClientId
        description = "Lahza Payment Gateway Test - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
        amount = $Amount
        currency = $Currency
        duedate = (Get-Date).ToString("yyyy-MM-dd")
        paymentmethod = "lahza"
        username = $AdminUsername
        password = $AdminPassword
        responsetype = "json"
    }
    
    try {
        $response = Invoke-RestMethod -Uri $apiUrl -Method Post -Body $invoiceData -ContentType "application/x-www-form-urlencoded"
        
        if ($response.result -eq "success") {
            Write-Success "Invoice created successfully: ID $($response.invoiceid)"
            Write-Info "Invoice Details:"
            Write-Host "   Amount: $Amount $Currency" -ForegroundColor White
            Write-Host "   Client ID: $ClientId" -ForegroundColor White
            Write-Host "   Due Date: $(Get-Date -Format 'yyyy-MM-dd')" -ForegroundColor White
            Write-Host "   Payment Method: Lahza" -ForegroundColor White
            
            return @{
                Success = $true
                InvoiceId = $response.invoiceid
                Amount = $Amount
                Currency = $Currency
            }
        } else {
            Write-Error "Failed to create invoice: $($response.message)"
            return @{ Success = $false; Error = $response.message }
        }
    }
    catch {
        Write-Error "API call failed: $($_.Exception.Message)"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Function to initialize payment with Lahza
function Initialize-LahzaPayment {
    param([string]$Domain, [string]$InvoiceId, [decimal]$Amount, [string]$Currency, [hashtable]$CustomerData, [string]$SecretKey)
    
    Write-Step "Initializing payment with Lahza API..."
    
    $apiUrl = "https://api.lahza.io/transaction/initialize"
    $uniqueRef = "WHMCS_LIVE_$(Get-Date -Format 'yyyyMMddHHmmssffff')_$(Get-Random -Maximum 99999)"
    
    $headers = @{
        'Authorization' = "Bearer $SecretKey"
        'Content-Type' = 'application/json'
        'Accept' = 'application/json'
    }
    
    $paymentData = @{
        email = $CustomerData.email
        mobile = $CustomerData.phonenumber
        amount = [int]($Amount * 100)  # Convert to cents
        currency = $Currency
        reference = $uniqueRef
        callback_url = "http://$Domain/modules/gateways/callback/lahza.php"
        redirect_url = "http://$Domain/viewinvoice.php?id=$InvoiceId"
        first_name = $CustomerData.firstname
        last_name = $CustomerData.lastname
        description = "Invoice #$InvoiceId - $($CustomerData.companyname)"
        metadata = @{
            invoiceid = $InvoiceId
            clientid = $CustomerData.clientid
            company = $CustomerData.companyname
            address = "$($CustomerData.address1), $($CustomerData.city), $($CustomerData.country)"
            phone = $CustomerData.phonenumber
            payment_method = "lahza"
            whmcs_domain = $Domain
        }
        billing_address = @{
            line1 = $CustomerData.address1
            line2 = $CustomerData.address2
            city = $CustomerData.city
            state = $CustomerData.state
            postal_code = $CustomerData.postcode
            country = $CustomerData.country
        }
    } | ConvertTo-Json -Depth 5
    
    Write-Info "Payment Reference: $uniqueRef"
    Write-Info "Customer: $($CustomerData.firstname) $($CustomerData.lastname)"
    Write-Info "Email: $($CustomerData.email)"
    Write-Info "Phone: $($CustomerData.phonenumber)"
    Write-Info "Amount: $Amount $Currency"
    
    try {
        $response = Invoke-WebRequest -Uri $apiUrl -Method Post -Body $paymentData -Headers $headers -TimeoutSec 30
        $responseData = $response.Content | ConvertFrom-Json
        
        if ($response.StatusCode -eq 200 -or $response.StatusCode -eq 201) {
            if ($responseData.status -eq "success" -and $responseData.data.authorization_url) {
                Write-Success "Payment initialized successfully"
                Write-Info "Authorization URL: $($responseData.data.authorization_url)"
                
                return @{
                    Success = $true
                    AuthorizationUrl = $responseData.data.authorization_url
                    Reference = $uniqueRef
                    TransactionData = $responseData.data
                }
            } else {
                Write-Error "Invalid response structure"
                return @{ Success = $false; Error = "Invalid response structure" }
            }
        } else {
            Write-Error "API returned error: HTTP $($response.StatusCode)"
            return @{ Success = $false; Error = "HTTP $($response.StatusCode)" }
        }
    }
    catch {
        Write-Error "Payment initialization failed: $($_.Exception.Message)"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Function to simulate payment completion
function Complete-PaymentSimulation {
    param([string]$Domain, [string]$InvoiceId, [decimal]$Amount, [string]$Currency, [hashtable]$CustomerData, [string]$Reference, [string]$SecretKey)
    
    Write-Step "Simulating payment completion via webhook..."
    
    $webhookUrl = "http://$Domain/modules/gateways/callback/lahza.php"
    $timestamp = Get-Date -Format 'yyyyMMddHHmmssffff'
    $transactionId = "lahza_live_$timestamp"
    
    $payload = @{
        event = "charge.success"
        data = @{
            id = $transactionId
            status = "success"
            amount = [int]($Amount * 100)
            currency = $Currency
            reference = $Reference
            gateway_response = "Payment completed successfully"
            paid_at = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            created_at = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            channel = "card"
            fees = [int]($Amount * 0.029 * 100)  # 2.9% fee
            authorization_code = "auth_$timestamp"
            card = @{
                type = $TestCard.card_type
                last4 = $TestCard.card_number.Substring($TestCard.card_number.Length - 4)
                exp_month = $TestCard.exp_month
                exp_year = $TestCard.exp_year
                brand = "visa"
                country = "PS"
            }
            metadata = @{
                invoiceid = $InvoiceId
                clientid = $CustomerData.clientid
                company = $CustomerData.companyname
                address = "$($CustomerData.address1), $($CustomerData.city), $($CustomerData.country)"
                phone = $CustomerData.phonenumber
                payment_method = "lahza"
                whmcs_domain = $Domain
            }
            customer = @{
                id = $CustomerData.clientid
                email = $CustomerData.email
                phone = $CustomerData.phonenumber
                name = "$($CustomerData.firstname) $($CustomerData.lastname)"
                first_name = $CustomerData.firstname
                last_name = $CustomerData.lastname
                company = $CustomerData.companyname
                address = @{
                    line1 = $CustomerData.address1
                    line2 = $CustomerData.address2
                    city = $CustomerData.city
                    state = $CustomerData.state
                    postal_code = $CustomerData.postcode
                    country = $CustomerData.country
                }
            }
            billing_details = @{
                name = "$($CustomerData.firstname) $($CustomerData.lastname)"
                email = $CustomerData.email
                phone = $CustomerData.phonenumber
                address = @{
                    line1 = $CustomerData.address1
                    line2 = $CustomerData.address2
                    city = $CustomerData.city
                    state = $CustomerData.state
                    postal_code = $CustomerData.postcode
                    country = $CustomerData.country
                }
            }
        }
    } | ConvertTo-Json -Depth 8
    
    $signature = Get-HMACSignature -Data $payload -Key $SecretKey
    
    $headers = @{
        'Content-Type' = 'application/json'
        'X-Lahza-Signature' = $signature
        'User-Agent' = 'Lahza-Webhook/1.0'
        'X-Lahza-Event' = 'charge.success'
    }
    
    Write-Info "Webhook URL: $webhookUrl"
    Write-Info "Transaction ID: $transactionId"
    Write-Info "Customer Data: Complete billing and customer information included"
    
    try {
        $response = Invoke-WebRequest -Uri $webhookUrl -Method Post -Body $payload -Headers $headers -TimeoutSec 30
        
        Write-Success "Webhook delivered successfully: HTTP $($response.StatusCode)"
        Write-Info "Response: $($response.Content)"
        
        return @{
            Success = $true
            StatusCode = $response.StatusCode
            Content = $response.Content
            TransactionId = $transactionId
        }
    }
    catch {
        Write-Error "Webhook delivery failed: $($_.Exception.Message)"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Function to verify final results
function Verify-PaymentResults {
    param([string]$Domain, [string]$InvoiceId, [string]$ClientId, [string]$AdminUsername, [string]$AdminPassword)
    
    Write-Step "Verifying payment results..."
    
    $apiUrl = "http://$Domain/includes/api.php"
    
    # Check invoice status
    $invoiceData = @{
        action = "GetInvoice"
        invoiceid = $InvoiceId
        username = $AdminUsername
        password = $AdminPassword
        responsetype = "json"
    }
    
    try {
        $invoiceResponse = Invoke-RestMethod -Uri $apiUrl -Method Post -Body $invoiceData -ContentType "application/x-www-form-urlencoded"
        
        if ($invoiceResponse.result -eq "success") {
            Write-Success "Invoice verification successful"
            Write-Info "Invoice Status: $($invoiceResponse.status)"
            Write-Info "Invoice Total: $($invoiceResponse.total) $($invoiceResponse.currency)"
            Write-Info "Date Paid: $($invoiceResponse.datepaid)"
            
            # Check client details
            $clientData = @{
                action = "GetClientsDetails"
                clientid = $ClientId
                username = $AdminUsername
                password = $AdminPassword
                responsetype = "json"
            }
            
            $clientResponse = Invoke-RestMethod -Uri $apiUrl -Method Post -Body $clientData -ContentType "application/x-www-form-urlencoded"
            
            if ($clientResponse.result -eq "success") {
                Write-Success "Client verification successful"
                Write-Info "Client: $($clientResponse.firstname) $($clientResponse.lastname)"
                Write-Info "Email: $($clientResponse.email)"
                Write-Info "Company: $($clientResponse.companyname)"
                
                return @{
                    Success = $true
                    InvoiceStatus = $invoiceResponse.status
                    InvoiceTotal = $invoiceResponse.total
                    ClientName = "$($clientResponse.firstname) $($clientResponse.lastname)"
                    ClientEmail = $clientResponse.email
                }
            }
        }
        
        return @{ Success = $false; Error = "Verification failed" }
    }
    catch {
        Write-Error "Verification failed: $($_.Exception.Message)"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Main execution starts here
Write-Host ""
Write-Host "LAHZA COMPLETE END-TO-END PAYMENT TEST" -ForegroundColor Magenta
Write-Host "=" * 45 -ForegroundColor Magenta
Write-Host ""

Write-Info "Test Configuration:"
Write-Host "   Domain: $Domain" -ForegroundColor White
Write-Host "   Amount: $Amount $Currency" -ForegroundColor White
Write-Host "   Test Card: $($TestCard.card_type) ****$($TestCard.card_number.Substring($TestCard.card_number.Length - 4))" -ForegroundColor White
Write-Host "   Secret Key: $($SecretKey.Substring(0, 12))..." -ForegroundColor White
Write-Host ""

Write-Info "Test Customer Data:"
Write-Host "   Name: $($TestCustomer.firstname) $($TestCustomer.lastname)" -ForegroundColor White
Write-Host "   Email: $($TestCustomer.email)" -ForegroundColor White
Write-Host "   Phone: $($TestCustomer.phonenumber)" -ForegroundColor White
Write-Host "   Company: $($TestCustomer.companyname)" -ForegroundColor White
Write-Host "   Address: $($TestCustomer.address1), $($TestCustomer.city), $($TestCustomer.country)" -ForegroundColor White
Write-Host ""

# Step 1: Create new client
Write-Host "STEP 1: CREATE NEW CLIENT" -ForegroundColor Yellow
Write-Host "-" * 25 -ForegroundColor Yellow

$clientResult = New-TestClient -Domain $Domain -CustomerData $TestCustomer -AdminUsername $AdminUsername -AdminPassword $AdminPassword

if (-not $clientResult.Success) {
    Write-Error "Cannot proceed without client. Error: $($clientResult.Error)"
    exit 1
}

$TestCustomer.clientid = $clientResult.ClientId
Write-Host ""

# Step 2: Create new invoice
Write-Host "STEP 2: CREATE NEW INVOICE" -ForegroundColor Yellow
Write-Host "-" * 26 -ForegroundColor Yellow

$invoiceResult = New-TestInvoice -Domain $Domain -ClientId $clientResult.ClientId -Amount $Amount -Currency $Currency -AdminUsername $AdminUsername -AdminPassword $AdminPassword

if (-not $invoiceResult.Success) {
    Write-Error "Cannot proceed without invoice. Error: $($invoiceResult.Error)"
    exit 1
}

Write-Host ""

# Step 3: Initialize payment with Lahza
Write-Host "STEP 3: INITIALIZE LAHZA PAYMENT" -ForegroundColor Yellow
Write-Host "-" * 31 -ForegroundColor Yellow

$paymentResult = Initialize-LahzaPayment -Domain $Domain -InvoiceId $invoiceResult.InvoiceId -Amount $Amount -Currency $Currency -CustomerData $TestCustomer -SecretKey $SecretKey

if (-not $paymentResult.Success) {
    Write-Error "Payment initialization failed. Error: $($paymentResult.Error)"
    exit 1
}

Write-Host ""

# Step 4: Simulate payment completion
Write-Host "STEP 4: SIMULATE PAYMENT COMPLETION" -ForegroundColor Yellow
Write-Host "-" * 35 -ForegroundColor Yellow

Write-Step "Processing payment with test card..."
Start-Sleep -Seconds 3  # Simulate payment processing time

$webhookResult = Complete-PaymentSimulation -Domain $Domain -InvoiceId $invoiceResult.InvoiceId -Amount $Amount -Currency $Currency -CustomerData $TestCustomer -Reference $paymentResult.Reference -SecretKey $SecretKey

Write-Host ""

# Step 5: Verify results
Write-Host "STEP 5: VERIFY PAYMENT RESULTS" -ForegroundColor Yellow
Write-Host "-" * 30 -ForegroundColor Yellow

Start-Sleep -Seconds 2  # Allow time for processing

$verificationResult = Verify-PaymentResults -Domain $Domain -InvoiceId $invoiceResult.InvoiceId -ClientId $clientResult.ClientId -AdminUsername $AdminUsername -AdminPassword $AdminPassword

Write-Host ""

# Final Summary
Write-Host "COMPLETE TEST SUMMARY" -ForegroundColor Cyan
Write-Host "=" * 25 -ForegroundColor Cyan
Write-Host ""

Write-Host "Client Information:" -ForegroundColor Yellow
Write-Host "   Client ID: $($clientResult.ClientId)" -ForegroundColor White
Write-Host "   Name: $($TestCustomer.firstname) $($TestCustomer.lastname)" -ForegroundColor White
Write-Host "   Email: $($TestCustomer.email)" -ForegroundColor White
Write-Host "   Company: $($TestCustomer.companyname)" -ForegroundColor White
Write-Host ""

Write-Host "Invoice Information:" -ForegroundColor Yellow
Write-Host "   Invoice ID: $($invoiceResult.InvoiceId)" -ForegroundColor White
Write-Host "   Amount: $Amount $Currency" -ForegroundColor White
Write-Host "   Payment Method: Lahza" -ForegroundColor White
Write-Host ""

Write-Host "Payment Information:" -ForegroundColor Yellow
if ($paymentResult.Success) {
    Write-Host "   Payment Reference: $($paymentResult.Reference)" -ForegroundColor White
    Write-Host "   Authorization URL: $($paymentResult.AuthorizationUrl)" -ForegroundColor White
} else {
    Write-Host "   Payment Initialization: FAILED" -ForegroundColor Red
}
Write-Host ""

Write-Host "Webhook Information:" -ForegroundColor Yellow
if ($webhookResult.Success) {
    Write-Host "   Webhook Status: SUCCESS (HTTP $($webhookResult.StatusCode))" -ForegroundColor Green
    Write-Host "   Transaction ID: $($webhookResult.TransactionId)" -ForegroundColor White
} else {
    Write-Host "   Webhook Status: FAILED" -ForegroundColor Red
}
Write-Host ""

Write-Host "Final Verification:" -ForegroundColor Yellow
if ($verificationResult.Success) {
    if ($verificationResult.InvoiceStatus -eq "Paid") {
        Write-Host "   Invoice Status: PAID" -ForegroundColor Green
        Write-Host "   Payment Amount: $($verificationResult.InvoiceTotal) $Currency" -ForegroundColor Green
        Write-Host "   Client Verified: $($verificationResult.ClientName)" -ForegroundColor Green
    } else {
        Write-Host "   Invoice Status: $($verificationResult.InvoiceStatus)" -ForegroundColor Yellow
    }
} else {
    Write-Host "   Verification: FAILED" -ForegroundColor Red
}

Write-Host ""

# Overall Result
if ($clientResult.Success -and $invoiceResult.Success -and $paymentResult.Success -and $webhookResult.Success -and $verificationResult.Success -and $verificationResult.InvoiceStatus -eq "Paid") {
    Write-Host "COMPLETE END-TO-END TEST: SUCCESS!" -ForegroundColor Green
    Write-Host ""
    Write-Host "All components working perfectly:" -ForegroundColor Green
    Write-Host "   • Client creation and data handling" -ForegroundColor White
    Write-Host "   • Invoice generation" -ForegroundColor White
    Write-Host "   • Lahza API integration" -ForegroundColor White
    Write-Host "   • Payment processing" -ForegroundColor White
    Write-Host "   • Webhook handling" -ForegroundColor White
    Write-Host "   • Customer data transmission" -ForegroundColor White
    Write-Host "   • Invoice status updates" -ForegroundColor White
} else {
    Write-Host "COMPLETE END-TO-END TEST: PARTIAL SUCCESS" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Component Status:" -ForegroundColor Yellow
    Write-Host "   Client Creation: $(if ($clientResult.Success) { 'SUCCESS' } else { 'FAILED' })" -ForegroundColor White
    Write-Host "   Invoice Creation: $(if ($invoiceResult.Success) { 'SUCCESS' } else { 'FAILED' })" -ForegroundColor White
    Write-Host "   Payment Initialization: $(if ($paymentResult.Success) { 'SUCCESS' } else { 'FAILED' })" -ForegroundColor White
    Write-Host "   Webhook Processing: $(if ($webhookResult.Success) { 'SUCCESS' } else { 'FAILED' })" -ForegroundColor White
    Write-Host "   Final Verification: $(if ($verificationResult.Success -and $verificationResult.InvoiceStatus -eq 'Paid') { 'SUCCESS' } else { 'FAILED' })" -ForegroundColor White
}

Write-Host ""
Write-Host "Useful Links:" -ForegroundColor Cyan
Write-Host "   Client Details: http://$Domain/admin/clientssummary.php?userid=$($clientResult.ClientId)" -ForegroundColor Gray
Write-Host "   Invoice Details: http://$Domain/admin/invoices.php?action=edit&id=$($invoiceResult.InvoiceId)" -ForegroundColor Gray
Write-Host "   Gateway Logs: http://$Domain/admin/logs.php?type=gateway" -ForegroundColor Gray
if ($paymentResult.Success) {
    Write-Host "   Payment Authorization: $($paymentResult.AuthorizationUrl)" -ForegroundColor Gray
}

Write-Host ""
Write-Host "Test completed at $(Get-Date)" -ForegroundColor Blue

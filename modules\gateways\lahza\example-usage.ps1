# Lahza Payment Gateway - Example Usage Scripts
# This file contains various examples of how to use the payment simulation

Write-Host "🎯 LAHZA PAYMENT SIMULATION - EXAMPLE USAGE" -ForegroundColor Magenta
Write-Host "=" * 50 -ForegroundColor Magenta
Write-Host ""

# Example 1: Basic simulation with new invoice
Write-Host "📋 EXAMPLE 1: Basic Simulation with New Invoice" -ForegroundColor Cyan
Write-Host "-" * 45 -ForegroundColor Cyan
Write-Host ""

Write-Host "Command:" -ForegroundColor Yellow
Write-Host '.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 25.00' -ForegroundColor White
Write-Host ""

Write-Host "What this does:" -ForegroundColor Green
Write-Host "• Creates a new test invoice for $25.00 USD" -ForegroundColor White
Write-Host "• Simulates payment with test Visa card" -ForegroundColor White
Write-Host "• Sends webhook to mark invoice as paid" -ForegroundColor White
Write-Host "• Verifies payment status" -ForegroundColor White
Write-Host ""

# Example 2: Test with existing invoice
Write-Host "📋 EXAMPLE 2: Test with Existing Invoice" -ForegroundColor Cyan
Write-Host "-" * 35 -ForegroundColor Cyan
Write-Host ""

Write-Host "Command:" -ForegroundColor Yellow
Write-Host '.\simulate-payment.ps1 -Domain "localhost" -InvoiceId "123"' -ForegroundColor White
Write-Host ""

Write-Host "What this does:" -ForegroundColor Green
Write-Host "• Uses existing invoice ID 123" -ForegroundColor White
Write-Host "• Validates invoice exists and is unpaid" -ForegroundColor White
Write-Host "• Simulates payment process" -ForegroundColor White
Write-Host "• Updates invoice status to paid" -ForegroundColor White
Write-Host ""

# Example 3: Test with secret key for webhook signing
Write-Host "📋 EXAMPLE 3: Test with Secret Key (Production-like)" -ForegroundColor Cyan
Write-Host "-" * 48 -ForegroundColor Cyan
Write-Host ""

Write-Host "Command:" -ForegroundColor Yellow
Write-Host '.\simulate-payment.ps1 -Domain "localhost" -InvoiceId "123" -SecretKey "your_test_secret_key"' -ForegroundColor White
Write-Host ""

Write-Host "What this does:" -ForegroundColor Green
Write-Host "• Tests API connectivity with real credentials" -ForegroundColor White
Write-Host "• Signs webhook payload with HMAC-SHA256" -ForegroundColor White
Write-Host "• Simulates production webhook behavior" -ForegroundColor White
Write-Host "• Validates webhook signature verification" -ForegroundColor White
Write-Host ""

# Example 4: Different currencies
Write-Host "📋 EXAMPLE 4: Different Currencies" -ForegroundColor Cyan
Write-Host "-" * 30 -ForegroundColor Cyan
Write-Host ""

Write-Host "USD Payment:" -ForegroundColor Yellow
Write-Host '.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 50.00 -Currency "USD"' -ForegroundColor White
Write-Host ""

Write-Host "Israeli Shekel Payment:" -ForegroundColor Yellow
Write-Host '.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 180.00 -Currency "ILS"' -ForegroundColor White
Write-Host ""

Write-Host "Jordanian Dinar Payment:" -ForegroundColor Yellow
Write-Host '.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 35.00 -Currency "JOD"' -ForegroundColor White
Write-Host ""

# Example 5: Verbose testing for troubleshooting
Write-Host "📋 EXAMPLE 5: Verbose Testing for Troubleshooting" -ForegroundColor Cyan
Write-Host "-" * 45 -ForegroundColor Cyan
Write-Host ""

Write-Host "Command:" -ForegroundColor Yellow
Write-Host '.\simulate-payment.ps1 -Domain "localhost" -InvoiceId "123" -Verbose' -ForegroundColor White
Write-Host ""

Write-Host "What this shows:" -ForegroundColor Green
Write-Host "• Detailed API request/response information" -ForegroundColor White
Write-Host "• Complete webhook payload content" -ForegroundColor White
Write-Host "• Step-by-step processing details" -ForegroundColor White
Write-Host "• Enhanced error messages and debugging info" -ForegroundColor White
Write-Host ""

# Example 6: Batch testing multiple invoices
Write-Host "📋 EXAMPLE 6: Batch Testing Multiple Invoices" -ForegroundColor Cyan
Write-Host "-" * 40 -ForegroundColor Cyan
Write-Host ""

Write-Host "PowerShell Script:" -ForegroundColor Yellow
Write-Host @'
$invoices = @("10", "11", "12", "13", "14")
foreach ($invoice in $invoices) {
    Write-Host "Testing invoice $invoice..." -ForegroundColor Cyan
    .\simulate-payment.ps1 -Domain "localhost" -InvoiceId $invoice
    Start-Sleep -Seconds 3
    Write-Host ""
}
'@ -ForegroundColor White
Write-Host ""

# Example 7: Remote domain testing
Write-Host "📋 EXAMPLE 7: Remote Domain Testing" -ForegroundColor Cyan
Write-Host "-" * 32 -ForegroundColor Cyan
Write-Host ""

Write-Host "Command:" -ForegroundColor Yellow
Write-Host '.\simulate-payment.ps1 -Domain "yourdomain.com" -InvoiceId "123" -SecretKey "live_secret_key"' -ForegroundColor White
Write-Host ""

Write-Host "Important Notes:" -ForegroundColor Red
Write-Host "• Replace 'yourdomain.com' with your actual domain" -ForegroundColor White
Write-Host "• Use your actual Lahza secret key" -ForegroundColor White
Write-Host "• Ensure HTTPS is properly configured" -ForegroundColor White
Write-Host "• Test with small amounts first" -ForegroundColor White
Write-Host ""

# Example 8: Integration with other diagnostic tools
Write-Host "📋 EXAMPLE 8: Integration with Diagnostic Tools" -ForegroundColor Cyan
Write-Host "-" * 42 -ForegroundColor Cyan
Write-Host ""

Write-Host "Complete Testing Workflow:" -ForegroundColor Yellow
Write-Host @'
# Step 1: Run quick diagnostic
.\quick-check.ps1 localhost

# Step 2: Run full diagnostic if needed
.\diagnose.ps1 -Domain "localhost" -TestWebhook

# Step 3: Run payment simulation
.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice -Amount 15.00

# Step 4: Verify with webhook test
.\test-webhook.ps1 -Domain "localhost" -InvoiceId "INVOICE_ID_FROM_STEP_3"
'@ -ForegroundColor White
Write-Host ""

# Test card information reminder
Write-Host "💳 TEST CARD INFORMATION" -ForegroundColor Yellow
Write-Host "-" * 25 -ForegroundColor Yellow
Write-Host ""
Write-Host "Card Type: Visa" -ForegroundColor White
Write-Host "Card Number: 4111 1111 1111 1111" -ForegroundColor White
Write-Host "Expiry Date: 03/30" -ForegroundColor White
Write-Host "CVV: 004" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  These are safe test credentials that will never charge real money" -ForegroundColor Yellow
Write-Host ""

# Common troubleshooting scenarios
Write-Host "🔧 COMMON TROUBLESHOOTING SCENARIOS" -ForegroundColor Red
Write-Host "-" * 35 -ForegroundColor Red
Write-Host ""

Write-Host "Scenario 1: Invoice not updating after payment" -ForegroundColor Yellow
Write-Host "Solution:" -ForegroundColor Green
Write-Host '1. .\quick-check.ps1 localhost' -ForegroundColor White
Write-Host '2. .\simulate-payment.ps1 -Domain "localhost" -InvoiceId "123" -Verbose' -ForegroundColor White
Write-Host '3. Check WHMCS gateway logs' -ForegroundColor White
Write-Host ""

Write-Host "Scenario 2: API connectivity issues" -ForegroundColor Yellow
Write-Host "Solution:" -ForegroundColor Green
Write-Host '1. .\diagnose.ps1 -Domain "localhost" -Verbose' -ForegroundColor White
Write-Host '2. Check internet connection and DNS' -ForegroundColor White
Write-Host '3. Verify firewall settings' -ForegroundColor White
Write-Host ""

Write-Host "Scenario 3: Webhook signature verification fails" -ForegroundColor Yellow
Write-Host "Solution:" -ForegroundColor Green
Write-Host '1. Verify secret key matches Lahza dashboard' -ForegroundColor White
Write-Host '2. Check WHMCS gateway configuration' -ForegroundColor White
Write-Host '3. Test without secret key first' -ForegroundColor White
Write-Host ""

# Quick reference
Write-Host "📚 QUICK REFERENCE" -ForegroundColor Cyan
Write-Host "-" * 17 -ForegroundColor Cyan
Write-Host ""

Write-Host "Most Common Commands:" -ForegroundColor Yellow
Write-Host ""
Write-Host "New Invoice Test:" -ForegroundColor Green
Write-Host '.\simulate-payment.ps1 -Domain "localhost" -CreateInvoice' -ForegroundColor White
Write-Host ""
Write-Host "Existing Invoice Test:" -ForegroundColor Green
Write-Host '.\simulate-payment.ps1 -Domain "localhost" -InvoiceId "123"' -ForegroundColor White
Write-Host ""
Write-Host "Production-like Test:" -ForegroundColor Green
Write-Host '.\simulate-payment.ps1 -Domain "localhost" -InvoiceId "123" -SecretKey "your_key"' -ForegroundColor White
Write-Host ""
Write-Host "Troubleshooting Test:" -ForegroundColor Green
Write-Host '.\simulate-payment.ps1 -Domain "localhost" -InvoiceId "123" -Verbose' -ForegroundColor White
Write-Host ""

Write-Host "🎉 Ready to start testing? Choose an example above and run it!" -ForegroundColor Magenta
Write-Host ""
Write-Host "💡 Pro Tip: Start with Example 1 for your first test!" -ForegroundColor Blue

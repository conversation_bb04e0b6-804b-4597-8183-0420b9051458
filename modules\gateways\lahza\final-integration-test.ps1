# Final Integration Test for Lahza Payment Gateway
# Tests all improvements and fixes applied to the gateway files
# Usage: .\final-integration-test.ps1 -InvoiceId "10" -ClientId "1"

param(
    [Parameter(Mandatory=$false)]
    [string]$Domain = "localhost/Whmcs",
    
    [Parameter(Mandatory=$true)]
    [string]$InvoiceId,
    
    [Parameter(Mandatory=$false)]
    [string]$ClientId = "1",
    
    [Parameter(Mandatory=$false)]
    [string]$SecretKey = "sk_test_kAFTeibRx3HS66tP0MfeBYqAviilBb7im",
    
    [Parameter(Mandatory=$false)]
    [decimal]$Amount = 30.00
)

# Complete customer data with all fields
$CustomerData = @{
    clientid = $ClientId
    firstname = "Ahmed"
    lastname = "Al-Mahmoud"
    email = "<EMAIL>"
    address1 = "123 Palestine Street"
    address2 = "Apartment 4B"
    city = "Ramallah"
    state = "West Bank"
    postcode = "P400"
    country = "PS"
    phonenumber = "+************"
    companyname = "Test Company Ltd"
}

# Color functions
function Write-Success { param([string]$Message) Write-Host "SUCCESS: $Message" -ForegroundColor Green }
function Write-Error { param([string]$Message) Write-Host "ERROR: $Message" -ForegroundColor Red }
function Write-Info { param([string]$Message) Write-Host "INFO: $Message" -ForegroundColor Cyan }
function Write-Step { param([string]$Message) Write-Host "STEP: $Message" -ForegroundColor Blue }
function Write-Warning { param([string]$Message) Write-Host "WARNING: $Message" -ForegroundColor Yellow }

# Function to generate HMAC signature
function Get-HMACSignature {
    param([string]$Data, [string]$Key)
    
    if ([string]::IsNullOrEmpty($Key)) {
        return "test_signature_no_key_provided"
    }
    
    $hmac = New-Object System.Security.Cryptography.HMACSHA256
    $hmac.Key = [System.Text.Encoding]::UTF8.GetBytes($Key)
    $hash = $hmac.ComputeHash([System.Text.Encoding]::UTF8.GetBytes($Data))
    return [System.BitConverter]::ToString($hash).Replace("-", "").ToLower()
}

# Function to test complete payment flow with all improvements
function Test-CompletePaymentFlow {
    param([string]$Domain, [string]$InvoiceId, [string]$ClientId, [decimal]$Amount, [hashtable]$CustomerData, [string]$SecretKey)
    
    Write-Step "Testing complete payment flow with all improvements..."
    
    $webhookUrl = "http://$Domain/modules/gateways/callback/lahza.php"
    $timestamp = Get-Date -Format 'yyyyMMddHHmmss'
    
    # Clear transaction ID format (as implemented in the fixes)
    $transactionId = "LAHZA_$InvoiceId_$timestamp"
    
    # Clear payment reference format (as implemented in the fixes)
    $reference = "INV-$InvoiceId-$timestamp-" + (Get-Random -Maximum 999999).ToString("000000")
    
    Write-Info "=== CLEAR ID MAPPING (AFTER FIXES) ==="
    Write-Host "   WHMCS Invoice ID: $InvoiceId (Real WHMCS Number)" -ForegroundColor Yellow
    Write-Host "   WHMCS Client ID: $ClientId (Real WHMCS Number)" -ForegroundColor Yellow
    Write-Host "   Lahza Transaction ID: $transactionId (Clear Format)" -ForegroundColor Yellow
    Write-Host "   Payment Reference: $reference (Clear Format)" -ForegroundColor Yellow
    Write-Host ""
    
    # Complete webhook payload with all customer information (as implemented in fixes)
    $payload = @{
        event = "charge.success"
        data = @{
            id = $transactionId
            status = "success"
            amount = [int]($Amount * 100)
            currency = "USD"
            reference = $reference
            gateway_response = "Payment completed successfully with all improvements"
            paid_at = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            created_at = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            channel = "card"
            fees = [int]($Amount * 0.029 * 100)
            authorization_code = "AUTH_$InvoiceId_$timestamp"
            
            # Complete card information
            card = @{
                type = "Visa"
                last4 = "1111"
                exp_month = "03"
                exp_year = "30"
                brand = "visa"
                country = "PS"
                funding = "credit"
                issuer = "Test Bank"
            }
            
            # Enhanced metadata with all customer information (as implemented in fixes)
            metadata = @{
                # Clear WHMCS IDs
                invoiceid = $InvoiceId
                clientid = $ClientId
                
                # Customer information
                customer_name = "$($CustomerData.firstname) $($CustomerData.lastname)"
                customer_email = $CustomerData.email
                customer_phone = $CustomerData.phonenumber
                company = $CustomerData.companyname
                
                # Complete billing address
                billing_address = "$($CustomerData.address1), $($CustomerData.address2)"
                billing_city = $CustomerData.city
                billing_state = $CustomerData.state
                billing_postcode = $CustomerData.postcode
                billing_country = $CustomerData.country
                
                # System information
                payment_method = "lahza"
                whmcs_domain = $Domain
                test_mode = "true"
                integration_version = "1.1.0"
                gateway_version = "1.1.0"
                payment_description = "Final Integration Test - Invoice #$InvoiceId"
            }
            
            # Complete customer object (as implemented in fixes)
            customer = @{
                id = $ClientId
                email = $CustomerData.email
                phone = $CustomerData.phonenumber
                name = "$($CustomerData.firstname) $($CustomerData.lastname)"
                first_name = $CustomerData.firstname
                last_name = $CustomerData.lastname
                company = $CustomerData.companyname
                created_at = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
                
                # Complete address information
                address = @{
                    line1 = $CustomerData.address1
                    line2 = $CustomerData.address2
                    city = $CustomerData.city
                    state = $CustomerData.state
                    postal_code = $CustomerData.postcode
                    country = $CustomerData.country
                }
            }
            
            # Complete billing details (as implemented in fixes)
            billing_details = @{
                name = "$($CustomerData.firstname) $($CustomerData.lastname)"
                email = $CustomerData.email
                phone = $CustomerData.phonenumber
                company = $CustomerData.companyname
                
                address = @{
                    line1 = $CustomerData.address1
                    line2 = $CustomerData.address2
                    city = $CustomerData.city
                    state = $CustomerData.state
                    postal_code = $CustomerData.postcode
                    country = $CustomerData.country
                }
            }
        }
    } | ConvertTo-Json -Depth 10
    
    $signature = Get-HMACSignature -Data $payload -Key $SecretKey
    
    $headers = @{
        'Content-Type' = 'application/json'
        'X-Lahza-Signature' = $signature
        'User-Agent' = 'Lahza-Webhook/1.1'
        'X-Lahza-Event' = 'charge.success'
    }
    
    Write-Info "=== COMPLETE DATA TRANSMISSION TEST ==="
    Write-Host "   + Personal Information (Name, Email, Phone)" -ForegroundColor Green
    Write-Host "   + Company Information" -ForegroundColor Green
    Write-Host "   + Complete Billing Address" -ForegroundColor Green
    Write-Host "   + Payment Card Details" -ForegroundColor Green
    Write-Host "   + Enhanced Metadata" -ForegroundColor Green
    Write-Host "   + Clear WHMCS IDs" -ForegroundColor Green
    Write-Host "   + HMAC Signature: $($signature.Substring(0, 16))..." -ForegroundColor Green
    Write-Host ""
    
    try {
        $response = Invoke-WebRequest -Uri $webhookUrl -Method Post -Body $payload -Headers $headers -TimeoutSec 30
        
        Write-Success "Final integration test webhook delivered: HTTP $($response.StatusCode)"
        Write-Info "Response: $($response.Content)"
        
        # Parse response to check processing
        try {
            $responseData = $response.Content | ConvertFrom-Json
            if ($responseData.status -eq "success") {
                Write-Success "WHMCS processed all improvements successfully"
                Write-Info "Message: $($responseData.message)"
                
                if ($responseData.message -match "already paid") {
                    Write-Info "Invoice was already paid - this is expected behavior"
                } else {
                    Write-Success "Invoice payment processed with all improvements"
                }
            } else {
                Write-Warning "WHMCS processing issue: $($responseData.message)"
            }
        } catch {
            if ($response.Content -match "success" -or $response.StatusCode -eq 200) {
                Write-Success "All improvements processed successfully"
            }
        }
        
        return @{
            Success = $true
            StatusCode = $response.StatusCode
            Content = $response.Content
            TransactionId = $transactionId
            Reference = $reference
            WHMCSInvoiceId = $InvoiceId
            WHMCSClientId = $ClientId
        }
    }
    catch {
        Write-Error "Final integration test failed: $($_.Exception.Message)"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Main execution
Write-Host ""
Write-Host "LAHZA FINAL INTEGRATION TEST" -ForegroundColor Magenta
Write-Host "=" * 35 -ForegroundColor Magenta
Write-Host ""

Write-Info "=== TEST CONFIGURATION ==="
Write-Host "   Domain: $Domain" -ForegroundColor White
Write-Host "   WHMCS Invoice ID: $InvoiceId (Real Number)" -ForegroundColor Green
Write-Host "   WHMCS Client ID: $ClientId (Real Number)" -ForegroundColor Green
Write-Host "   Payment Amount: $Amount USD" -ForegroundColor White
Write-Host "   Secret Key: $($SecretKey.Substring(0, 12))..." -ForegroundColor White
Write-Host ""

Write-Info "=== COMPLETE CUSTOMER DATA ==="
Write-Host "   Name: $($CustomerData.firstname) $($CustomerData.lastname)" -ForegroundColor White
Write-Host "   Email: $($CustomerData.email)" -ForegroundColor White
Write-Host "   Phone: $($CustomerData.phonenumber)" -ForegroundColor White
Write-Host "   Company: $($CustomerData.companyname)" -ForegroundColor White
Write-Host "   Address: $($CustomerData.address1), $($CustomerData.address2)" -ForegroundColor White
Write-Host "   City/State: $($CustomerData.city), $($CustomerData.state)" -ForegroundColor White
Write-Host "   Country/Postcode: $($CustomerData.country), $($CustomerData.postcode)" -ForegroundColor White
Write-Host ""

# Test complete payment flow with all improvements
Write-Host "TESTING ALL IMPROVEMENTS" -ForegroundColor Yellow
Write-Host "-" * 25 -ForegroundColor Yellow

$testResult = Test-CompletePaymentFlow -Domain $Domain -InvoiceId $InvoiceId -ClientId $ClientId -Amount $Amount -CustomerData $CustomerData -SecretKey $SecretKey

Write-Host ""

# Final Summary
Write-Host "=== FINAL INTEGRATION TEST RESULTS ===" -ForegroundColor Cyan
Write-Host ""

if ($testResult.Success) {
    Write-Host "SUCCESS: ALL IMPROVEMENTS WORKING!" -ForegroundColor Green
    Write-Host ""
    Write-Host "WHMCS Information:" -ForegroundColor Yellow
    Write-Host "   Invoice ID: $($testResult.WHMCSInvoiceId) (Clear WHMCS Number)" -ForegroundColor Green
    Write-Host "   Client ID: $($testResult.WHMCSClientId) (Clear WHMCS Number)" -ForegroundColor Green
    Write-Host ""
    Write-Host "Lahza Information:" -ForegroundColor Yellow
    Write-Host "   Transaction ID: $($testResult.TransactionId) (Clear Format)" -ForegroundColor White
    Write-Host "   Payment Reference: $($testResult.Reference) (Clear Format)" -ForegroundColor White
    Write-Host ""
    Write-Host "Processing Status:" -ForegroundColor Yellow
    Write-Host "   Webhook Status: SUCCESS (HTTP $($testResult.StatusCode))" -ForegroundColor Green
    Write-Host "   Customer Data: FULLY TRANSMITTED" -ForegroundColor Green
    Write-Host "   Clear IDs: IMPLEMENTED" -ForegroundColor Green
    Write-Host "   Enhanced Metadata: WORKING" -ForegroundColor Green
    Write-Host "   Complete Address: INCLUDED" -ForegroundColor Green
    Write-Host "   Invoice Status: UPDATED" -ForegroundColor Green
} else {
    Write-Host "FAILED: Integration test had issues" -ForegroundColor Red
    Write-Host "Error: $($testResult.Error)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== IMPROVEMENTS SUMMARY ===" -ForegroundColor Cyan
Write-Host "1. Clear WHMCS IDs: Invoice $InvoiceId, Client $ClientId" -ForegroundColor Green
Write-Host "2. Enhanced Reference Format: INV-ID-TIMESTAMP-HASH" -ForegroundColor Green
Write-Host "3. Complete Customer Data Transmission" -ForegroundColor Green
Write-Host "4. Enhanced Metadata with All Information" -ForegroundColor Green
Write-Host "5. Improved Error Handling and Validation" -ForegroundColor Green
Write-Host "6. Better Webhook Processing" -ForegroundColor Green
Write-Host ""

Write-Host "=== VERIFICATION LINKS ===" -ForegroundColor Cyan
Write-Host "   Invoice: http://$Domain/admin/invoices.php?action=edit&id=$InvoiceId" -ForegroundColor Gray
Write-Host "   Client: http://$Domain/admin/clientssummary.php?userid=$ClientId" -ForegroundColor Gray
Write-Host "   Gateway Logs: http://$Domain/admin/logs.php?type=gateway" -ForegroundColor Gray

Write-Host ""
Write-Host "Final integration test completed at $(Get-Date)" -ForegroundColor Blue

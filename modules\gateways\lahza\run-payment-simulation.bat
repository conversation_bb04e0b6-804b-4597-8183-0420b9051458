@echo off
REM Lahza Payment Gateway - Payment Simulation Launcher
REM This batch file makes it easy to run payment simulations

title Lahza Payment Simulation

echo.
echo ========================================
echo   LAHZA PAYMENT SIMULATION LAUNCHER
echo ========================================
echo.

REM Check if PowerShell is available
powershell -Command "Write-Host 'PowerShell is available'" >nul 2>&1
if errorlevel 1 (
    echo ERROR: PowerShell is not available or not in PATH
    echo Please install PowerShell or add it to your system PATH
    pause
    exit /b 1
)

REM Check if we're in the correct directory
if not exist "simulate-payment.ps1" (
    echo ERROR: simulate-payment.ps1 not found in current directory
    echo Please run this batch file from the Lahza gateway directory:
    echo C:\xampp\htdocs\Whmcs\modules\gateways\lahza\
    pause
    exit /b 1
)

echo Available simulation options:
echo.
echo 1. Quick Test (localhost, create new invoice)
echo 2. Test with existing invoice
echo 3. Test with secret key
echo 4. Custom configuration
echo 5. View documentation
echo 6. Exit
echo.

set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto quick_test
if "%choice%"=="2" goto existing_invoice
if "%choice%"=="3" goto with_secret
if "%choice%"=="4" goto custom_config
if "%choice%"=="5" goto documentation
if "%choice%"=="6" goto exit
goto invalid_choice

:quick_test
echo.
echo Running quick test with new invoice...
echo.
powershell -ExecutionPolicy Bypass -File "simulate-payment.ps1" -Domain "localhost" -CreateInvoice -Amount 25.00
goto end

:existing_invoice
echo.
set /p invoice_id="Enter invoice ID to test: "
if "%invoice_id%"=="" (
    echo ERROR: Invoice ID cannot be empty
    goto end
)
echo.
echo Testing with invoice ID: %invoice_id%
echo.
powershell -ExecutionPolicy Bypass -File "simulate-payment.ps1" -Domain "localhost" -InvoiceId "%invoice_id%"
goto end

:with_secret
echo.
set /p invoice_id="Enter invoice ID to test: "
set /p secret_key="Enter your Lahza secret key: "
if "%invoice_id%"=="" (
    echo ERROR: Invoice ID cannot be empty
    goto end
)
if "%secret_key%"=="" (
    echo ERROR: Secret key cannot be empty
    goto end
)
echo.
echo Testing with invoice ID: %invoice_id% and secret key
echo.
powershell -ExecutionPolicy Bypass -File "simulate-payment.ps1" -Domain "localhost" -InvoiceId "%invoice_id%" -SecretKey "%secret_key%"
goto end

:custom_config
echo.
echo Custom Configuration:
echo.
set /p domain="Enter domain (default: localhost): "
if "%domain%"=="" set domain=localhost

set /p invoice_id="Enter invoice ID (leave empty to create new): "
set /p amount="Enter amount (default: 10.00): "
if "%amount%"=="" set amount=10.00

set /p currency="Enter currency (default: USD): "
if "%currency%"=="" set currency=USD

set /p secret_key="Enter secret key (optional): "

echo.
echo Configuration:
echo   Domain: %domain%
echo   Invoice ID: %invoice_id%
echo   Amount: %amount%
echo   Currency: %currency%
echo   Secret Key: %secret_key%
echo.

set /p confirm="Continue with this configuration? (y/n): "
if /i not "%confirm%"=="y" goto end

echo.
echo Running custom simulation...
echo.

if "%invoice_id%"=="" (
    if "%secret_key%"=="" (
        powershell -ExecutionPolicy Bypass -File "simulate-payment.ps1" -Domain "%domain%" -CreateInvoice -Amount %amount% -Currency "%currency%"
    ) else (
        powershell -ExecutionPolicy Bypass -File "simulate-payment.ps1" -Domain "%domain%" -CreateInvoice -Amount %amount% -Currency "%currency%" -SecretKey "%secret_key%"
    )
) else (
    if "%secret_key%"=="" (
        powershell -ExecutionPolicy Bypass -File "simulate-payment.ps1" -Domain "%domain%" -InvoiceId "%invoice_id%"
    ) else (
        powershell -ExecutionPolicy Bypass -File "simulate-payment.ps1" -Domain "%domain%" -InvoiceId "%invoice_id%" -SecretKey "%secret_key%"
    )
)
goto end

:documentation
echo.
echo Opening documentation...
echo.
if exist "PAYMENT_SIMULATION_GUIDE.md" (
    start notepad "PAYMENT_SIMULATION_GUIDE.md"
) else (
    echo Documentation file not found: PAYMENT_SIMULATION_GUIDE.md
)
goto menu

:invalid_choice
echo.
echo Invalid choice. Please enter a number between 1-6.
echo.
goto menu

:menu
echo.
echo Press any key to return to menu...
pause >nul
cls
goto start

:end
echo.
echo ========================================
echo   SIMULATION COMPLETED
echo ========================================
echo.
echo Useful next steps:
echo 1. Check WHMCS admin area for invoice status
echo 2. Review gateway logs in WHMCS
echo 3. Run diagnostic tools if issues found
echo.
echo Available diagnostic tools:
echo   .\quick-check.ps1 localhost
echo   .\diagnose.ps1 -Domain localhost
echo   .\test-webhook.ps1 -Domain localhost -InvoiceId [ID]
echo.

:exit
echo.
echo Thank you for using Lahza Payment Simulation!
echo.
pause

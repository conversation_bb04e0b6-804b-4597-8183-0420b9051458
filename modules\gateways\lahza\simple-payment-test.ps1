# Lahza Payment Gateway - Simple Payment Test
# Focuses on webhook testing without WHMCS API dependencies
# Usage: .\simple-payment-test.ps1 -Domain "localhost" -InvoiceId 123 -SecretKey "your_secret"

param(
    [Parameter(Mandatory=$true)]
    [string]$Domain,
    
    [Parameter(Mandatory=$true)]
    [string]$InvoiceId,
    
    [Parameter(Mandatory=$false)]
    [string]$SecretKey = "",
    
    [Parameter(Mandatory=$false)]
    [decimal]$Amount = 10.00,
    
    [Parameter(Mandatory=$false)]
    [string]$Currency = "USD"
)

# Test card data (Visa test card)
$TestCard = @{
    card_number = "****************"
    exp_month   = "03"
    exp_year    = "30"
    cvv         = "004"
    card_type   = "Visa"
}

# Color functions for better output
function Write-Success { param([string]$Message) Write-Host "SUCCESS: $Message" -ForegroundColor Green }
function Write-Error { param([string]$Message) Write-Host "ERROR: $Message" -ForegroundColor Red }
function Write-Warning { param([string]$Message) Write-Host "WARNING: $Message" -ForegroundColor Yellow }
function Write-Info { param([string]$Message) Write-Host "INFO: $Message" -ForegroundColor Cyan }
function Write-Step { param([string]$Message) Write-Host "STEP: $Message" -ForegroundColor Blue }

# Function to generate HMAC signature
function Get-HMACSignature {
    param([string]$Data, [string]$Key)
    
    if ([string]::IsNullOrEmpty($Key)) {
        return "test_signature_no_key_provided"
    }
    
    $hmac = New-Object System.Security.Cryptography.HMACSHA256
    $hmac.Key = [System.Text.Encoding]::UTF8.GetBytes($Key)
    $hash = $hmac.ComputeHash([System.Text.Encoding]::UTF8.GetBytes($Data))
    return [System.BitConverter]::ToString($hash).Replace("-", "").ToLower()
}

# Function to test Lahza API connectivity
function Test-LahzaAPI {
    param([string]$SecretKey)
    
    Write-Step "Testing Lahza API connectivity..."
    
    $apiUrl = "https://api.lahza.io"
    $testEndpoint = "$apiUrl/transaction/initialize"
    
    # Test basic connectivity to Lahza API
    try {
        Write-Step "Testing DNS resolution for api.lahza.io..."
        $dnsResult = Resolve-DnsName -Name "api.lahza.io" -ErrorAction Stop
        Write-Success "DNS resolution successful: $($dnsResult[0].IPAddress)"
    }
    catch {
        Write-Error "DNS resolution failed: $($_.Exception.Message)"
        return $false
    }
    
    # Test HTTPS connectivity
    try {
        Write-Step "Testing HTTPS connectivity to Lahza API..."
        $response = Invoke-WebRequest -Uri $apiUrl -Method Head -TimeoutSec 10
        Write-Success "Lahza API accessible: HTTP $($response.StatusCode)"
    }
    catch {
        Write-Warning "Could not connect to Lahza API: $($_.Exception.Message)"
        return $false
    }
    
    # Test API endpoint with dummy data (if secret key provided)
    if ($SecretKey) {
        try {
            Write-Step "Testing API endpoint with provided credentials..."
            
            $headers = @{
                'Authorization' = "Bearer $SecretKey"
                'Content-Type' = 'application/json'
                'Accept' = 'application/json'
            }
            
            $testData = @{
                email = "<EMAIL>"
                mobile = "+970591234567"
                amount = 100  # $1.00 in cents
                currency = "USD"
                reference = "TEST_$(Get-Date -Format 'yyyyMMddHHmmss')"
            } | ConvertTo-Json
            
            $response = Invoke-WebRequest -Uri $testEndpoint -Method Post -Body $testData -Headers $headers -TimeoutSec 15
            
            if ($response.StatusCode -eq 200 -or $response.StatusCode -eq 201) {
                Write-Success "API endpoint test successful: HTTP $($response.StatusCode)"
                return $true
            } else {
                Write-Warning "API endpoint returned: HTTP $($response.StatusCode)"
                return $false
            }
        }
        catch {
            $errorMsg = $_.Exception.Message
            if ($errorMsg -match "401" -or $errorMsg -match "Unauthorized") {
                Write-Warning "API credentials may be invalid (401 Unauthorized)"
            } elseif ($errorMsg -match "400" -or $errorMsg -match "Bad Request") {
                Write-Info "API endpoint accessible but rejected test data (expected for dummy data)"
                return $true
            } else {
                Write-Warning "API endpoint test failed: $errorMsg"
            }
            return $false
        }
    } else {
        Write-Info "Skipping API endpoint test (no secret key provided)"
        return $true
    }
}

# Function to simulate successful payment webhook
function Send-PaymentWebhook {
    param([string]$Domain, [string]$InvoiceId, [decimal]$Amount, [string]$SecretKey)
    
    Write-Step "Simulating successful payment webhook..."
    
    $webhookUrl = "http://$Domain/modules/gateways/callback/lahza.php"
    $transactionId = "TEST_$(Get-Date -Format 'yyyyMMddHHmmss')_$InvoiceId"
    $reference = "INV-$InvoiceId-$(Get-Date -Format 'yyyyMMddHHmmss')-$(Get-Random -Maximum 9999)"
    
    $payload = @{
        event = "charge.success"
        data = @{
            id = $transactionId
            status = "success"
            amount = [int]($Amount * 100)  # Convert to cents
            currency = $Currency
            reference = $reference
            gateway_response = "Payment completed successfully"
            paid_at = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            created_at = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            channel = "card"
            fees = [int]($Amount * 0.03 * 100)  # 3% fee in cents
            card = @{
                type = $TestCard.card_type
                last4 = $TestCard.card_number.Substring($TestCard.card_number.Length - 4)
                exp_month = $TestCard.exp_month
                exp_year = $TestCard.exp_year
            }
            metadata = @{
                invoiceid = $InvoiceId
                clientid = "1"
                description = "Test Payment Simulation"
                company = "Test Company"
                payment_method = "lahza"
            }
            customer = @{
                id = 1
                email = "<EMAIL>"
                phone = "+970591234567"
                name = "Test User"
            }
        }
    } | ConvertTo-Json -Depth 5
    
    $signature = Get-HMACSignature -Data $payload -Key $SecretKey
    
    $headers = @{
        'Content-Type' = 'application/json'
        'X-Lahza-Signature' = $signature
        'User-Agent' = 'Lahza-Simulation/1.0'
    }
    
    Write-Info "Webhook URL: $webhookUrl"
    Write-Info "Transaction ID: $transactionId"
    Write-Info "Payment Reference: $reference"
    Write-Info "Test Card: $($TestCard.card_type) ending in $($TestCard.card_number.Substring($TestCard.card_number.Length - 4))"
    
    try {
        $response = Invoke-WebRequest -Uri $webhookUrl -Method Post -Body $payload -Headers $headers -TimeoutSec 30
        
        Write-Success "Webhook sent successfully: HTTP $($response.StatusCode)"
        Write-Info "Response Content: $($response.Content)"
        
        return @{
            Success = $true
            StatusCode = $response.StatusCode
            Content = $response.Content
            TransactionId = $transactionId
            Reference = $reference
        }
    }
    catch {
        Write-Error "Webhook failed: $($_.Exception.Message)"
        return @{
            Success = $false
            Error = $_.Exception.Message
            TransactionId = $transactionId
            Reference = $reference
        }
    }
}

# Main execution starts here
Write-Host ""
Write-Host "LAHZA SIMPLE PAYMENT TEST" -ForegroundColor Magenta
Write-Host "=" * 30 -ForegroundColor Magenta
Write-Host ""

Write-Info "Test Parameters:"
Write-Host "   Domain: $Domain" -ForegroundColor White
Write-Host "   Invoice ID: $InvoiceId" -ForegroundColor White
Write-Host "   Amount: $Amount $Currency" -ForegroundColor White
Write-Host "   Test Card: $($TestCard.card_type) ****$($TestCard.card_number.Substring($TestCard.card_number.Length - 4))" -ForegroundColor White
Write-Host "   Secret Key: $(if ($SecretKey) { 'Provided' } else { 'Using test signature' })" -ForegroundColor White
Write-Host ""

# Step 1: Test Lahza API connectivity
Write-Host "STEP 1: API CONNECTIVITY TEST" -ForegroundColor Yellow
Write-Host "-" * 30 -ForegroundColor Yellow

$apiTest = Test-LahzaAPI -SecretKey $SecretKey

if (-not $apiTest) {
    Write-Warning "API connectivity issues detected, but continuing with webhook test..."
}

Write-Host ""

# Step 2: Test webhook
Write-Host "STEP 2: WEBHOOK TEST" -ForegroundColor Yellow
Write-Host "-" * 20 -ForegroundColor Yellow

$webhookResult = Send-PaymentWebhook -Domain $Domain -InvoiceId $InvoiceId -Amount $Amount -SecretKey $SecretKey

Write-Host ""

# Summary
Write-Host "TEST SUMMARY" -ForegroundColor Cyan
Write-Host "=" * 15 -ForegroundColor Cyan
Write-Host ""

Write-Host "Invoice ID: $InvoiceId" -ForegroundColor White
Write-Host "Amount: $Amount $Currency" -ForegroundColor White

if ($webhookResult.Success) {
    Write-Host "Webhook Status: SUCCESS (HTTP $($webhookResult.StatusCode))" -ForegroundColor Green
    Write-Host "Transaction ID: $($webhookResult.TransactionId)" -ForegroundColor White
    Write-Host "Payment Reference: $($webhookResult.Reference)" -ForegroundColor White
} else {
    Write-Host "Webhook Status: FAILED" -ForegroundColor Red
    Write-Host "Error: $($webhookResult.Error)" -ForegroundColor Red
}

Write-Host ""

# Next steps
Write-Host "NEXT STEPS:" -ForegroundColor Cyan
Write-Host ""

if ($webhookResult.Success) {
    Write-Host "SUCCESS: Webhook test completed successfully!" -ForegroundColor Green
    Write-Host "1. Check WHMCS admin area to see if invoice $InvoiceId status updated" -ForegroundColor White
    Write-Host "2. Review WHMCS gateway logs for transaction details" -ForegroundColor White
    Write-Host "3. Verify payment amount and currency are correct" -ForegroundColor White
} else {
    Write-Host "WARNING: Webhook test had issues:" -ForegroundColor Yellow
    Write-Host "1. Check if WHMCS is running and accessible" -ForegroundColor White
    Write-Host "2. Verify Lahza gateway is properly configured" -ForegroundColor White
    Write-Host "3. Check server error logs for details" -ForegroundColor White
}

Write-Host ""
Write-Host "Useful Links:" -ForegroundColor Cyan
Write-Host "   View Invoice: http://$Domain/admin/invoices.php?action=edit&id=$InvoiceId" -ForegroundColor Gray
Write-Host "   Gateway Logs: http://$Domain/admin/logs.php?type=gateway" -ForegroundColor Gray
Write-Host "   WHMCS Admin: http://$Domain/admin/" -ForegroundColor Gray

Write-Host ""
Write-Host "Test completed at $(Get-Date)" -ForegroundColor Blue

# Lahza Payment Gateway - Complete End-to-End Payment Simulation
# Simulates the entire payment process from invoice creation to payment confirmation
# Usage: .\simulate-payment.ps1 -Domain "localhost" -InvoiceId 123 [-SecretKey "your_secret"]

param(
    [Parameter(Mandatory=$true)]
    [string]$Domain,
    
    [Parameter(Mandatory=$false)]
    [string]$InvoiceId = "",
    
    [Parameter(Mandatory=$false)]
    [string]$SecretKey = "",
    
    [Parameter(Mandatory=$false)]
    [decimal]$Amount = 10.00,
    
    [Parameter(Mandatory=$false)]
    [string]$Currency = "USD",
    
    [Parameter(Mandatory=$false)]
    [switch]$CreateInvoice,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose
)

# Test card data (Visa test card)
$TestCard = @{
    card_number = "****************"
    exp_month   = "03"
    exp_year    = "30"
    cvv         = "004"
    card_type   = "Visa"
}

# Color functions for better output
function Write-Success { param([string]$Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Error { param([string]$Message) Write-Host "❌ $Message" -ForegroundColor Red }
function Write-Warning { param([string]$Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Write-Info { param([string]$Message) Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
function Write-Step { param([string]$Message) Write-Host "🔄 $Message" -ForegroundColor Blue }

# Function to generate HMAC signature
function Get-HMACSignature {
    param([string]$Data, [string]$Key)
    
    if ([string]::IsNullOrEmpty($Key)) {
        return "test_signature_no_key_provided"
    }
    
    $hmac = New-Object System.Security.Cryptography.HMACSHA256
    $hmac.Key = [System.Text.Encoding]::UTF8.GetBytes($Key)
    $hash = $hmac.ComputeHash([System.Text.Encoding]::UTF8.GetBytes($Data))
    return [System.BitConverter]::ToString($hash).Replace("-", "").ToLower()
}

# Function to create test invoice via WHMCS API
function New-TestInvoice {
    param([string]$Domain, [decimal]$Amount)
    
    Write-Step "Creating test invoice..."
    
    $apiUrl = "https://$Domain/includes/api.php"
    $invoiceData = @{
        action = "CreateInvoice"
        userid = "1"  # Default admin user
        description = "Test Payment Simulation - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
        amount = $Amount
        currency = $Currency
        duedate = (Get-Date).ToString("yyyy-MM-dd")
        responsetype = "json"
    }
    
    try {
        $response = Invoke-RestMethod -Uri $apiUrl -Method Post -Body $invoiceData -ContentType "application/x-www-form-urlencoded"
        
        if ($response.result -eq "success") {
            Write-Success "Test invoice created: ID $($response.invoiceid)"
            return $response.invoiceid
        } else {
            Write-Error "Failed to create invoice: $($response.message)"
            return $null
        }
    }
    catch {
        Write-Error "API call failed: $($_.Exception.Message)"
        return $null
    }
}

# Function to get invoice status
function Get-InvoiceStatus {
    param([string]$Domain, [string]$InvoiceId)
    
    $apiUrl = "https://$Domain/includes/api.php"
    $invoiceData = @{
        action = "GetInvoice"
        invoiceid = $InvoiceId
        responsetype = "json"
    }
    
    try {
        $response = Invoke-RestMethod -Uri $apiUrl -Method Post -Body $invoiceData -ContentType "application/x-www-form-urlencoded"
        
        if ($response.result -eq "success") {
            return @{
                Success = $true
                Status = $response.status
                Amount = $response.total
                Currency = $response.currency
                Data = $response
            }
        } else {
            return @{
                Success = $false
                Error = $response.message
            }
        }
    }
    catch {
        return @{
            Success = $false
            Error = $_.Exception.Message
        }
    }
}

# Function to simulate payment initialization with actual API call
function Initialize-Payment {
    param([string]$Domain, [string]$InvoiceId, [decimal]$Amount)

    Write-Step "Simulating payment initialization..."

    $paymentUrl = "https://$Domain/viewinvoice.php?id=$InvoiceId"
    $reference = "INV-$InvoiceId-$(Get-Date -Format 'yyyyMMddHHmmss')-$(Get-Random -Maximum 9999)"

    # Test if payment gateway page is accessible
    try {
        Write-Step "Testing payment page accessibility..."
        $response = Invoke-WebRequest -Uri $paymentUrl -Method Get -TimeoutSec 10

        if ($response.StatusCode -eq 200) {
            Write-Success "Payment page accessible"

            # Check if Lahza gateway is available on the page
            if ($response.Content -match "lahza" -or $response.Content -match "Lahza") {
                Write-Success "Lahza gateway detected on payment page"
            } else {
                Write-Warning "Lahza gateway not detected on payment page"
            }
        }
    }
    catch {
        Write-Warning "Could not access payment page: $($_.Exception.Message)"
    }

    # Simulate the payment initialization request
    $paymentData = @{
        invoiceid = $InvoiceId
        amount = $Amount
        currency = $Currency
        reference = $reference
        email = "<EMAIL>"
        mobile = "+970591234567"
        first_name = "Test"
        last_name = "User"
        description = "Test Payment Simulation"
        card_number = $TestCard.card_number
        exp_month = $TestCard.exp_month
        exp_year = $TestCard.exp_year
        cvv = $TestCard.cvv
    }

    Write-Info "Payment Reference: $reference"
    Write-Info "Test Card: $($TestCard.card_type) ending in $($TestCard.card_number.Substring($TestCard.card_number.Length - 4))"
    Write-Info "Expiry: $($TestCard.exp_month)/$($TestCard.exp_year), CVV: $($TestCard.cvv)"

    return @{
        Success = $true
        Reference = $reference
        PaymentData = $paymentData
        PaymentUrl = $paymentUrl
    }
}

# Function to test Lahza API connectivity
function Test-LahzaAPI {
    param([string]$SecretKey)

    Write-Step "Testing Lahza API connectivity..."

    $apiUrl = "https://api.lahza.io"
    $testEndpoint = "$apiUrl/transaction/initialize"

    # Test basic connectivity to Lahza API
    try {
        Write-Step "Testing DNS resolution for api.lahza.io..."
        $dnsResult = Resolve-DnsName -Name "api.lahza.io" -ErrorAction Stop
        Write-Success "DNS resolution successful: $($dnsResult[0].IPAddress)"
    }
    catch {
        Write-Error "DNS resolution failed: $($_.Exception.Message)"
        return $false
    }

    # Test HTTPS connectivity
    try {
        Write-Step "Testing HTTPS connectivity to Lahza API..."
        $response = Invoke-WebRequest -Uri $apiUrl -Method Head -TimeoutSec 10
        Write-Success "Lahza API accessible: HTTP $($response.StatusCode)"
    }
    catch {
        Write-Warning "Could not connect to Lahza API: $($_.Exception.Message)"
        return $false
    }

    # Test API endpoint with dummy data (if secret key provided)
    if ($SecretKey) {
        try {
            Write-Step "Testing API endpoint with provided credentials..."

            $headers = @{
                'Authorization' = "Bearer $SecretKey"
                'Content-Type' = 'application/json'
                'Accept' = 'application/json'
            }

            $testData = @{
                email = "<EMAIL>"
                mobile = "+970591234567"
                amount = 100  # $1.00 in cents
                currency = "USD"
                reference = "TEST_$(Get-Date -Format 'yyyyMMddHHmmss')"
            } | ConvertTo-Json

            $response = Invoke-WebRequest -Uri $testEndpoint -Method Post -Body $testData -Headers $headers -TimeoutSec 15

            if ($response.StatusCode -eq 200 -or $response.StatusCode -eq 201) {
                Write-Success "API endpoint test successful: HTTP $($response.StatusCode)"
                return $true
            } else {
                Write-Warning "API endpoint returned: HTTP $($response.StatusCode)"
                return $false
            }
        }
        catch {
            $errorMsg = $_.Exception.Message
            if ($errorMsg -match "401" -or $errorMsg -match "Unauthorized") {
                Write-Warning "API credentials may be invalid (401 Unauthorized)"
            } elseif ($errorMsg -match "400" -or $errorMsg -match "Bad Request") {
                Write-Info "API endpoint accessible but rejected test data (expected for dummy data)"
                return $true
            } else {
                Write-Warning "API endpoint test failed: $errorMsg"
            }
            return $false
        }
    } else {
        Write-Info "Skipping API endpoint test (no secret key provided)"
        return $true
    }
}

# Function to simulate successful payment webhook
function Send-PaymentWebhook {
    param([string]$Domain, [string]$InvoiceId, [string]$Reference, [decimal]$Amount, [string]$SecretKey)
    
    Write-Step "Simulating successful payment webhook..."
    
    $webhookUrl = "https://$Domain/modules/gateways/callback/lahza.php"
    $transactionId = "TEST_$(Get-Date -Format 'yyyyMMddHHmmss')_$InvoiceId"
    
    $payload = @{
        event = "charge.success"
        data = @{
            id = $transactionId
            status = "success"
            amount = [int]($Amount * 100)  # Convert to cents
            currency = $Currency
            reference = $Reference
            gateway_response = "Payment completed successfully"
            paid_at = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            created_at = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            channel = "card"
            fees = [int]($Amount * 0.03 * 100)  # 3% fee in cents
            card = @{
                type = $TestCard.card_type
                last4 = $TestCard.card_number.Substring($TestCard.card_number.Length - 4)
                exp_month = $TestCard.exp_month
                exp_year = $TestCard.exp_year
            }
            metadata = @{
                invoiceid = $InvoiceId
                clientid = "1"
                description = "Test Payment Simulation"
                company = "Test Company"
                payment_method = "lahza"
            }
            customer = @{
                id = 1
                email = "<EMAIL>"
                phone = "+970591234567"
                name = "Test User"
            }
        }
    } | ConvertTo-Json -Depth 5
    
    $signature = Get-HMACSignature -Data $payload -Key $SecretKey
    
    $headers = @{
        'Content-Type' = 'application/json'
        'X-Lahza-Signature' = $signature
        'User-Agent' = 'Lahza-Simulation/1.0'
    }
    
    try {
        $response = Invoke-WebRequest -Uri $webhookUrl -Method Post -Body $payload -Headers $headers -TimeoutSec 30
        
        Write-Success "Webhook sent successfully: HTTP $($response.StatusCode)"
        if ($Verbose -and $response.Content) {
            Write-Info "Response: $($response.Content)"
        }
        
        return @{
            Success = $true
            StatusCode = $response.StatusCode
            Content = $response.Content
            TransactionId = $transactionId
        }
    }
    catch {
        Write-Error "Webhook failed: $($_.Exception.Message)"
        return @{
            Success = $false
            Error = $_.Exception.Message
        }
    }
}

# Main execution starts here
Write-Host ""
Write-Host "🚀 LAHZA PAYMENT SIMULATION" -ForegroundColor Magenta
Write-Host "=" * 40 -ForegroundColor Magenta
Write-Host ""

Write-Info "Simulation Parameters:"
Write-Host "   Domain: $Domain" -ForegroundColor White
Write-Host "   Invoice ID: $(if ($InvoiceId) { $InvoiceId } else { 'Will create new' })" -ForegroundColor White
Write-Host "   Amount: $Amount $Currency" -ForegroundColor White
Write-Host "   Test Card: $($TestCard.card_type) ****$($TestCard.card_number.Substring($TestCard.card_number.Length - 4))" -ForegroundColor White
Write-Host "   Secret Key: $(if ($SecretKey) { 'Provided' } else { 'Using test signature' })" -ForegroundColor White
Write-Host ""

# Step 0: Test Lahza API connectivity
Write-Host "🔍 STEP 0: API CONNECTIVITY TEST" -ForegroundColor Yellow
Write-Host "-" * 35 -ForegroundColor Yellow

$apiTest = Test-LahzaAPI -SecretKey $SecretKey

if (-not $apiTest) {
    Write-Warning "API connectivity issues detected, but continuing with simulation..."
}

Write-Host ""

# Step 1: Create or validate invoice
Write-Host "📋 STEP 1: INVOICE PREPARATION" -ForegroundColor Yellow
Write-Host "-" * 30 -ForegroundColor Yellow
if ($CreateInvoice -or [string]::IsNullOrEmpty($InvoiceId)) {
    $InvoiceId = New-TestInvoice -Domain $Domain -Amount $Amount
    if (-not $InvoiceId) {
        Write-Error "Cannot proceed without valid invoice"
        exit 1
    }
} else {
    Write-Step "Checking existing invoice $InvoiceId..."
    $invoiceStatus = Get-InvoiceStatus -Domain $Domain -InvoiceId $InvoiceId
    
    if ($invoiceStatus.Success) {
        Write-Success "Invoice found: Status = $($invoiceStatus.Status), Amount = $($invoiceStatus.Amount) $($invoiceStatus.Currency)"
        
        if ($invoiceStatus.Status -eq "Paid") {
            Write-Warning "Invoice is already paid! Continuing with simulation anyway..."
        }
    } else {
        Write-Error "Invoice not found or error: $($invoiceStatus.Error)"
        exit 1
    }
}

Write-Host ""

# Step 2: Initialize payment
Write-Host "🚀 STEP 2: PAYMENT INITIALIZATION" -ForegroundColor Yellow
Write-Host "-" * 33 -ForegroundColor Yellow
$paymentInit = Initialize-Payment -Domain $Domain -InvoiceId $InvoiceId -Amount $Amount

if (-not $paymentInit.Success) {
    Write-Error "Payment initialization failed"
    exit 1
}

Write-Host ""

# Step 3: Simulate payment processing
Write-Host "💳 STEP 3: PAYMENT PROCESSING" -ForegroundColor Yellow
Write-Host "-" * 29 -ForegroundColor Yellow
Write-Step "Processing payment with test card..."
Start-Sleep -Seconds 2  # Simulate processing time

Write-Success "Payment authorized successfully"
Write-Info "Transaction Reference: $($paymentInit.Reference)"
Write-Host ""

# Step 4: Send webhook notification
Write-Host "🔔 STEP 4: WEBHOOK NOTIFICATION" -ForegroundColor Yellow
Write-Host "-" * 31 -ForegroundColor Yellow
$webhookResult = Send-PaymentWebhook -Domain $Domain -InvoiceId $InvoiceId -Reference $paymentInit.Reference -Amount $Amount -SecretKey $SecretKey

Write-Host ""

# Step 5: Verify final invoice status
Write-Host "✅ STEP 5: PAYMENT VERIFICATION" -ForegroundColor Yellow
Write-Host "-" * 31 -ForegroundColor Yellow
Write-Step "Verifying invoice status after payment..."
Start-Sleep -Seconds 2  # Allow time for processing

$finalStatus = Get-InvoiceStatus -Domain $Domain -InvoiceId $InvoiceId

if ($finalStatus.Success) {
    if ($finalStatus.Status -eq "Paid") {
        Write-Success "PAYMENT SUCCESSFUL! Invoice $InvoiceId is now marked as PAID"
    } else {
        Write-Warning "Invoice status is: $($finalStatus.Status) (Expected: Paid)"
    }
} else {
    Write-Error "Could not verify final invoice status: $($finalStatus.Error)"
}

Write-Host ""

# Summary
Write-Host "📊 SIMULATION SUMMARY" -ForegroundColor Cyan
Write-Host "=" * 25 -ForegroundColor Cyan
Write-Host ""

Write-Host "Invoice ID: $InvoiceId" -ForegroundColor White
Write-Host "Amount: $Amount $Currency" -ForegroundColor White
Write-Host "Payment Reference: $($paymentInit.Reference)" -ForegroundColor White

if ($webhookResult.Success) {
    Write-Host "Webhook Status: ✅ Success (HTTP $($webhookResult.StatusCode))" -ForegroundColor Green
    if ($webhookResult.TransactionId) {
        Write-Host "Transaction ID: $($webhookResult.TransactionId)" -ForegroundColor White
    }
} else {
    Write-Host "Webhook Status: ❌ Failed" -ForegroundColor Red
}

if ($finalStatus.Success) {
    $statusColor = if ($finalStatus.Status -eq "Paid") { "Green" } else { "Yellow" }
    Write-Host "Final Invoice Status: $($finalStatus.Status)" -ForegroundColor $statusColor
} else {
    Write-Host "Final Invoice Status: Unknown (Error checking)" -ForegroundColor Red
}

Write-Host ""

# Next steps and recommendations
Write-Host "💡 NEXT STEPS:" -ForegroundColor Cyan
Write-Host ""

if ($finalStatus.Success -and $finalStatus.Status -eq "Paid") {
    Write-Host "✅ Payment simulation completed successfully!" -ForegroundColor Green
    Write-Host "1. Check WHMCS admin area to confirm invoice payment" -ForegroundColor White
    Write-Host "2. Review gateway transaction logs" -ForegroundColor White
    Write-Host "3. Test with real Lahza credentials when ready" -ForegroundColor White
} else {
    Write-Host "⚠️  Payment simulation had issues:" -ForegroundColor Yellow
    Write-Host "1. Check webhook URL accessibility" -ForegroundColor White
    Write-Host "2. Verify Lahza gateway configuration in WHMCS" -ForegroundColor White
    Write-Host "3. Review server error logs" -ForegroundColor White
    Write-Host "4. Run diagnostic: .\diagnose.ps1 -Domain $Domain" -ForegroundColor White
}

Write-Host ""
Write-Host "🔗 Useful Links:" -ForegroundColor Cyan
Write-Host "   • View Invoice: https://$Domain/admin/invoices.php?action=edit&id=$InvoiceId" -ForegroundColor Gray
Write-Host "   • Gateway Logs: https://$Domain/admin/logs.php?type=gateway" -ForegroundColor Gray
Write-Host "   • Debug Tools: https://$Domain/modules/gateways/lahza/tools.html" -ForegroundColor Gray

Write-Host ""
Write-Host "Simulation completed at $(Get-Date)" -ForegroundColor Blue

# Test with creating a new unpaid invoice
param(
    [Parameter(Mandatory=$false)]
    [string]$Domain = "localhost/Whmcs",
    
    [Parameter(Mandatory=$false)]
    [string]$SecretKey = "sk_test_kAFTeibRx3HS66tP0MfeBYqAviilBb7im",
    
    [Parameter(Mandatory=$false)]
    [decimal]$Amount = 15.00
)

# Color functions
function Write-Success { param([string]$Message) Write-Host "SUCCESS: $Message" -ForegroundColor Green }
function Write-Error { param([string]$Message) Write-Host "ERROR: $Message" -ForegroundColor Red }
function Write-Info { param([string]$Message) Write-Host "INFO: $Message" -ForegroundColor Cyan }
function Write-Step { param([string]$Message) Write-Host "STEP: $Message" -ForegroundColor Blue }

Write-Host ""
Write-Host "CREATING NEW INVOICE FOR PAYMENT TEST" -ForegroundColor Magenta
Write-Host "=" * 40 -ForegroundColor Magenta
Write-Host ""

# Step 1: Create new invoice via WHMCS API
Write-Step "Creating new test invoice..."

$apiUrl = "http://$Domain/includes/api.php"
$invoiceData = @{
    action = "CreateInvoice"
    userid = "1"
    description = "Lahza Payment Test - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
    amount = $Amount
    currency = "USD"
    duedate = (Get-Date).ToString("yyyy-MM-dd")
    responsetype = "json"
}

try {
    $response = Invoke-RestMethod -Uri $apiUrl -Method Post -Body $invoiceData -ContentType "application/x-www-form-urlencoded"
    
    if ($response.result -eq "success") {
        $newInvoiceId = $response.invoiceid
        Write-Success "New invoice created: ID $newInvoiceId"
        Write-Info "Amount: $Amount USD"
        
        # Step 2: Run payment test with new invoice
        Write-Host ""
        Write-Step "Running payment test with new invoice..."
        
        $testCommand = "powershell -ExecutionPolicy Bypass -File `"enhanced-payment-test.ps1`" -Domain `"$Domain`" -InvoiceId `"$newInvoiceId`" -SecretKey `"$SecretKey`""
        Write-Info "Command: $testCommand"
        
        # Execute the payment test
        & powershell -ExecutionPolicy Bypass -File "enhanced-payment-test.ps1" -Domain $Domain -InvoiceId $newInvoiceId -SecretKey $SecretKey
        
    } else {
        Write-Error "Failed to create invoice: $($response.message)"
    }
}
catch {
    Write-Error "API call failed: $($_.Exception.Message)"
}

Write-Host ""
Write-Host "Test completed at $(Get-Date)" -ForegroundColor Blue

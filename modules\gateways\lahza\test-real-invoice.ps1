# Test with a real existing invoice
param(
    [Parameter(Mandatory=$false)]
    [string]$Domain = "localhost/Whmcs",
    
    [Parameter(Mandatory=$true)]
    [string]$InvoiceId,
    
    [Parameter(Mandatory=$false)]
    [string]$SecretKey = "sk_test_kAFTeibRx3HS66tP0MfeBYqAviilBb7im",
    
    [Parameter(Mandatory=$false)]
    [decimal]$Amount = 25.00
)

# Complete customer data
$CustomerData = @{
    clientid = "1"
    firstname = "<PERSON>"
    lastname = "<PERSON>-<PERSON><PERSON><PERSON>"
    email = "ahmed.test.$(Get-Date -Format 'yyyyMMddHHmmss')@example.com"
    address1 = "123 Palestine Street"
    address2 = "Apartment 4B"
    city = "Ramallah"
    state = "West Bank"
    postcode = "P400"
    country = "PS"
    phonenumber = "+************"
    companyname = "Test Company Ltd"
}

# Color functions
function Write-Success { param([string]$Message) Write-Host "SUCCESS: $Message" -ForegroundColor Green }
function Write-Error { param([string]$Message) Write-Host "ERROR: $Message" -ForegroundColor Red }
function Write-Info { param([string]$Message) Write-Host "INFO: $Message" -ForegroundColor Cyan }
function Write-Step { param([string]$Message) Write-Host "STEP: $Message" -ForegroundColor Blue }

# Function to generate HMAC signature
function Get-HMACSignature {
    param([string]$Data, [string]$Key)
    
    if ([string]::IsNullOrEmpty($Key)) {
        return "test_signature_no_key_provided"
    }
    
    $hmac = New-Object System.Security.Cryptography.HMACSHA256
    $hmac.Key = [System.Text.Encoding]::UTF8.GetBytes($Key)
    $hash = $hmac.ComputeHash([System.Text.Encoding]::UTF8.GetBytes($Data))
    return [System.BitConverter]::ToString($hash).Replace("-", "").ToLower()
}

# Function to send payment webhook for real invoice
function Send-RealInvoiceWebhook {
    param([string]$Domain, [string]$InvoiceId, [decimal]$Amount, [hashtable]$CustomerData, [string]$SecretKey)
    
    Write-Step "Sending payment webhook for real invoice $InvoiceId..."
    
    $webhookUrl = "http://$Domain/modules/gateways/callback/lahza.php"
    $timestamp = Get-Date -Format 'yyyyMMddHHmmssffff'
    $transactionId = "lahza_real_$timestamp"
    $reference = "REAL_INV_$InvoiceId_$timestamp"
    
    # Complete webhook payload with all customer information
    $payload = @{
        event = "charge.success"
        data = @{
            id = $transactionId
            status = "success"
            amount = [int]($Amount * 100)
            currency = "USD"
            reference = $reference
            gateway_response = "Payment completed successfully - Real Invoice Test"
            paid_at = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            created_at = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            channel = "card"
            fees = [int]($Amount * 0.029 * 100)  # 2.9% fee
            authorization_code = "auth_real_$timestamp"
            
            # Complete card information
            card = @{
                type = "Visa"
                last4 = "1111"
                exp_month = "03"
                exp_year = "30"
                brand = "visa"
                country = "PS"
                funding = "credit"
                issuer = "Test Bank"
            }
            
            # Complete metadata with all customer information
            metadata = @{
                invoiceid = $InvoiceId
                clientid = $CustomerData.clientid
                company = $CustomerData.companyname
                customer_name = "$($CustomerData.firstname) $($CustomerData.lastname)"
                customer_email = $CustomerData.email
                customer_phone = $CustomerData.phonenumber
                billing_address = "$($CustomerData.address1), $($CustomerData.address2)"
                billing_city = $CustomerData.city
                billing_state = $CustomerData.state
                billing_postcode = $CustomerData.postcode
                billing_country = $CustomerData.country
                payment_method = "lahza"
                whmcs_domain = $Domain
                test_mode = "true"
                integration_version = "1.0"
                payment_description = "Real Invoice Payment Test - Invoice #$InvoiceId"
            }
            
            # Complete customer object
            customer = @{
                id = $CustomerData.clientid
                email = $CustomerData.email
                phone = $CustomerData.phonenumber
                name = "$($CustomerData.firstname) $($CustomerData.lastname)"
                first_name = $CustomerData.firstname
                last_name = $CustomerData.lastname
                company = $CustomerData.companyname
                created_at = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
                
                # Complete address information
                address = @{
                    line1 = $CustomerData.address1
                    line2 = $CustomerData.address2
                    city = $CustomerData.city
                    state = $CustomerData.state
                    postal_code = $CustomerData.postcode
                    country = $CustomerData.country
                }
            }
            
            # Complete billing details
            billing_details = @{
                name = "$($CustomerData.firstname) $($CustomerData.lastname)"
                email = $CustomerData.email
                phone = $CustomerData.phonenumber
                company = $CustomerData.companyname
                
                address = @{
                    line1 = $CustomerData.address1
                    line2 = $CustomerData.address2
                    city = $CustomerData.city
                    state = $CustomerData.state
                    postal_code = $CustomerData.postcode
                    country = $CustomerData.country
                }
            }
        }
    } | ConvertTo-Json -Depth 10
    
    $signature = Get-HMACSignature -Data $payload -Key $SecretKey
    
    $headers = @{
        'Content-Type' = 'application/json'
        'X-Lahza-Signature' = $signature
        'User-Agent' = 'Lahza-Webhook/1.0'
        'X-Lahza-Event' = 'charge.success'
    }
    
    Write-Info "Webhook URL: $webhookUrl"
    Write-Info "Real Invoice ID: $InvoiceId"
    Write-Info "Transaction ID: $transactionId"
    Write-Info "Payment Reference: $reference"
    Write-Info "Complete Customer Data Included"
    Write-Info "HMAC Signature: $($signature.Substring(0, 16))..."
    
    try {
        $response = Invoke-WebRequest -Uri $webhookUrl -Method Post -Body $payload -Headers $headers -TimeoutSec 30
        
        Write-Success "Webhook delivered successfully: HTTP $($response.StatusCode)"
        Write-Info "Response Content: $($response.Content)"
        
        # Parse response to check processing
        try {
            $responseData = $response.Content | ConvertFrom-Json
            if ($responseData.status -eq "success") {
                Write-Success "WHMCS processed payment successfully"
                Write-Info "Message: $($responseData.message)"
                
                if ($responseData.message -match "already paid") {
                    Write-Info "Invoice was already paid - this is expected behavior"
                } else {
                    Write-Success "Invoice payment processed and status updated"
                }
            } else {
                Write-Error "WHMCS processing issue: $($responseData.message)"
            }
        } catch {
            if ($response.Content -match "success" -or $response.StatusCode -eq 200) {
                Write-Success "Webhook processed successfully"
            } else {
                Write-Error "Unexpected response format: $($response.Content)"
            }
        }
        
        return @{
            Success = $true
            StatusCode = $response.StatusCode
            Content = $response.Content
            TransactionId = $transactionId
            Reference = $reference
        }
    }
    catch {
        Write-Error "Webhook delivery failed: $($_.Exception.Message)"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Function to check invoice status after payment
function Check-InvoiceStatus {
    param([string]$Domain, [string]$InvoiceId)
    
    Write-Step "Checking invoice status after payment..."
    
    # Try to check via direct database query simulation
    Write-Info "Checking invoice $InvoiceId status in WHMCS..."
    
    # Since we can't access WHMCS API directly, we'll check the gateway logs
    Write-Info "To verify payment status, please check:"
    Write-Host "   1. WHMCS Admin: http://$Domain/admin/invoices.php?action=edit&id=$InvoiceId" -ForegroundColor Yellow
    Write-Host "   2. Gateway Logs: http://$Domain/admin/logs.php?type=gateway" -ForegroundColor Yellow
    Write-Host "   3. Look for transaction ID in logs" -ForegroundColor Yellow
    
    return $true
}

# Main execution
Write-Host ""
Write-Host "LAHZA REAL INVOICE PAYMENT TEST" -ForegroundColor Magenta
Write-Host "=" * 35 -ForegroundColor Magenta
Write-Host ""

Write-Info "Test Configuration:"
Write-Host "   Domain: $Domain" -ForegroundColor White
Write-Host "   Real Invoice ID: $InvoiceId" -ForegroundColor White
Write-Host "   Amount: $Amount USD" -ForegroundColor White
Write-Host "   Secret Key: $($SecretKey.Substring(0, 12))..." -ForegroundColor White
Write-Host ""

Write-Info "Customer Data for Payment:"
Write-Host "   Name: $($CustomerData.firstname) $($CustomerData.lastname)" -ForegroundColor White
Write-Host "   Email: $($CustomerData.email)" -ForegroundColor White
Write-Host "   Phone: $($CustomerData.phonenumber)" -ForegroundColor White
Write-Host "   Company: $($CustomerData.companyname)" -ForegroundColor White
Write-Host "   Address: $($CustomerData.address1), $($CustomerData.address2)" -ForegroundColor White
Write-Host "   City/State: $($CustomerData.city), $($CustomerData.state)" -ForegroundColor White
Write-Host "   Country: $($CustomerData.country), Postcode: $($CustomerData.postcode)" -ForegroundColor White
Write-Host ""

# Send payment webhook for real invoice
Write-Host "STEP 1: PROCESS PAYMENT FOR REAL INVOICE" -ForegroundColor Yellow
Write-Host "-" * 40 -ForegroundColor Yellow

$webhookResult = Send-RealInvoiceWebhook -Domain $Domain -InvoiceId $InvoiceId -Amount $Amount -CustomerData $CustomerData -SecretKey $SecretKey

Write-Host ""

# Check invoice status
Write-Host "STEP 2: VERIFY INVOICE STATUS UPDATE" -ForegroundColor Yellow
Write-Host "-" * 35 -ForegroundColor Yellow

$statusCheck = Check-InvoiceStatus -Domain $Domain -InvoiceId $InvoiceId

Write-Host ""

# Final Summary
Write-Host "REAL INVOICE TEST SUMMARY" -ForegroundColor Cyan
Write-Host "=" * 28 -ForegroundColor Cyan
Write-Host ""

Write-Host "Invoice Information:" -ForegroundColor Yellow
Write-Host "   Invoice ID: $InvoiceId" -ForegroundColor White
Write-Host "   Amount: $Amount USD" -ForegroundColor White
Write-Host "   Client ID: $($CustomerData.clientid)" -ForegroundColor White
Write-Host ""

Write-Host "Payment Processing:" -ForegroundColor Yellow
if ($webhookResult.Success) {
    Write-Host "   Webhook Delivery: SUCCESS (HTTP $($webhookResult.StatusCode))" -ForegroundColor Green
    Write-Host "   Transaction ID: $($webhookResult.TransactionId)" -ForegroundColor White
    Write-Host "   Payment Reference: $($webhookResult.Reference)" -ForegroundColor White
    Write-Host "   Customer Data: FULLY TRANSMITTED" -ForegroundColor Green
} else {
    Write-Host "   Webhook Delivery: FAILED" -ForegroundColor Red
    Write-Host "   Error: $($webhookResult.Error)" -ForegroundColor Red
}

Write-Host ""

if ($webhookResult.Success) {
    Write-Host "SUCCESS: Real invoice payment test completed!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next Steps:" -ForegroundColor Cyan
    Write-Host "1. Check WHMCS admin to verify invoice $InvoiceId is marked as 'Paid'" -ForegroundColor White
    Write-Host "2. Review gateway logs for transaction details" -ForegroundColor White
    Write-Host "3. Confirm all customer data was processed correctly" -ForegroundColor White
} else {
    Write-Host "WARNING: Payment test had issues" -ForegroundColor Yellow
    Write-Host "Please check WHMCS configuration and try again" -ForegroundColor White
}

Write-Host ""
Write-Host "Verification Links:" -ForegroundColor Cyan
Write-Host "   Invoice: http://$Domain/admin/invoices.php?action=edit&id=$InvoiceId" -ForegroundColor Gray
Write-Host "   Gateway Logs: http://$Domain/admin/logs.php?type=gateway" -ForegroundColor Gray
Write-Host "   Client Details: http://$Domain/admin/clientssummary.php?userid=$($CustomerData.clientid)" -ForegroundColor Gray

Write-Host ""
Write-Host "Test completed at $(Get-Date)" -ForegroundColor Blue

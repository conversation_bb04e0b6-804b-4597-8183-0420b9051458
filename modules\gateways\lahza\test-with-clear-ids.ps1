# Test with clear WHMCS IDs and proper reference format
param(
    [Parameter(Mandatory=$false)]
    [string]$Domain = "localhost/Whmcs",
    
    [Parameter(Mandatory=$true)]
    [string]$InvoiceId,
    
    [Parameter(Mandatory=$false)]
    [string]$ClientId = "1",
    
    [Parameter(Mandatory=$false)]
    [string]$SecretKey = "sk_test_kAFTeibRx3HS66tP0MfeBYqAviilBb7im",
    
    [Parameter(Mandatory=$false)]
    [decimal]$Amount = 30.00
)

# Customer data with clear WHMCS IDs
$CustomerData = @{
    clientid = $ClientId  # WHMCS Client ID (رقم صحيح)
    firstname = "<PERSON>"
    lastname = "<PERSON><PERSON><PERSON><PERSON><PERSON>"
    email = "<EMAIL>"
    address1 = "123 Palestine Street"
    address2 = "Apartment 4B"
    city = "Ramallah"
    state = "West Bank"
    postcode = "P400"
    country = "PS"
    phonenumber = "+************"
    companyname = "Test Company Ltd"
}

# Color functions
function Write-Success { param([string]$Message) Write-Host "SUCCESS: $Message" -ForegroundColor Green }
function Write-Error { param([string]$Message) Write-Host "ERROR: $Message" -ForegroundColor Red }
function Write-Info { param([string]$Message) Write-Host "INFO: $Message" -ForegroundColor Cyan }
function Write-Step { param([string]$Message) Write-Host "STEP: $Message" -ForegroundColor Blue }

# Function to generate HMAC signature
function Get-HMACSignature {
    param([string]$Data, [string]$Key)
    
    if ([string]::IsNullOrEmpty($Key)) {
        return "test_signature_no_key_provided"
    }
    
    $hmac = New-Object System.Security.Cryptography.HMACSHA256
    $hmac.Key = [System.Text.Encoding]::UTF8.GetBytes($Key)
    $hash = $hmac.ComputeHash([System.Text.Encoding]::UTF8.GetBytes($Data))
    return [System.BitConverter]::ToString($hash).Replace("-", "").ToLower()
}

# Function to send payment webhook with clear WHMCS IDs
function Send-ClearIDWebhook {
    param([string]$Domain, [string]$InvoiceId, [string]$ClientId, [decimal]$Amount, [hashtable]$CustomerData, [string]$SecretKey)
    
    Write-Step "Sending payment webhook with clear WHMCS IDs..."
    
    $webhookUrl = "http://$Domain/modules/gateways/callback/lahza.php"
    $timestamp = Get-Date -Format 'yyyyMMddHHmmss'
    
    # Clear transaction ID format
    $transactionId = "LAHZA_$InvoiceId_$timestamp"
    
    # Clear payment reference format (using WHMCS invoice ID)
    $reference = "INV-$InvoiceId-$timestamp"
    
    Write-Info "=== CLEAR ID MAPPING ==="
    Write-Host "   WHMCS Invoice ID: $InvoiceId (Real WHMCS Invoice Number)" -ForegroundColor Yellow
    Write-Host "   WHMCS Client ID: $ClientId (Real WHMCS Client Number)" -ForegroundColor Yellow
    Write-Host "   Lahza Transaction ID: $transactionId (Lahza Transaction Identifier)" -ForegroundColor Yellow
    Write-Host "   Payment Reference: $reference (Payment Reference)" -ForegroundColor Yellow
    Write-Host ""
    
    # Complete webhook payload with clear IDs
    $payload = @{
        event = "charge.success"
        data = @{
            id = $transactionId
            status = "success"
            amount = [int]($Amount * 100)
            currency = "USD"
            reference = $reference
            gateway_response = "Payment completed successfully"
            paid_at = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            created_at = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            channel = "card"
            fees = [int]($Amount * 0.029 * 100)
            authorization_code = "AUTH_$InvoiceId_$timestamp"
            
            # Card information
            card = @{
                type = "Visa"
                last4 = "1111"
                exp_month = "03"
                exp_year = "30"
                brand = "visa"
                country = "PS"
            }
            
            # Metadata with clear WHMCS IDs
            metadata = @{
                # Real WHMCS Numbers
                invoiceid = $InvoiceId           # Real Invoice Number
                clientid = $ClientId             # Real Client Number
                
                # بيانات العميل
                company = $CustomerData.companyname
                customer_name = "$($CustomerData.firstname) $($CustomerData.lastname)"
                customer_email = $CustomerData.email
                customer_phone = $CustomerData.phonenumber
                
                # العنوان
                billing_address = "$($CustomerData.address1), $($CustomerData.address2)"
                billing_city = $CustomerData.city
                billing_state = $CustomerData.state
                billing_postcode = $CustomerData.postcode
                billing_country = $CustomerData.country
                
                # معلومات النظام
                payment_method = "lahza"
                whmcs_domain = $Domain
                test_mode = "true"
                integration_version = "1.0"
                
                # معلومات إضافية
                payment_description = "Payment for Invoice #$InvoiceId"
                transaction_type = "payment"
                processor = "lahza"
            }
            
            # Customer object with WHMCS ID
            customer = @{
                id = $ClientId                    # Real WHMCS Client ID
                email = $CustomerData.email
                phone = $CustomerData.phonenumber
                name = "$($CustomerData.firstname) $($CustomerData.lastname)"
                first_name = $CustomerData.firstname
                last_name = $CustomerData.lastname
                company = $CustomerData.companyname
                
                # Complete address
                address = @{
                    line1 = $CustomerData.address1
                    line2 = $CustomerData.address2
                    city = $CustomerData.city
                    state = $CustomerData.state
                    postal_code = $CustomerData.postcode
                    country = $CustomerData.country
                }
            }
            
            # Billing details
            billing_details = @{
                name = "$($CustomerData.firstname) $($CustomerData.lastname)"
                email = $CustomerData.email
                phone = $CustomerData.phonenumber
                company = $CustomerData.companyname
                
                address = @{
                    line1 = $CustomerData.address1
                    line2 = $CustomerData.address2
                    city = $CustomerData.city
                    state = $CustomerData.state
                    postal_code = $CustomerData.postcode
                    country = $CustomerData.country
                }
            }
        }
    } | ConvertTo-Json -Depth 10
    
    $signature = Get-HMACSignature -Data $payload -Key $SecretKey
    
    $headers = @{
        'Content-Type' = 'application/json'
        'X-Lahza-Signature' = $signature
        'User-Agent' = 'Lahza-Webhook/1.0'
        'X-Lahza-Event' = 'charge.success'
    }
    
    Write-Info "=== WEBHOOK DETAILS ==="
    Write-Host "   Webhook URL: $webhookUrl" -ForegroundColor White
    Write-Host "   HMAC Signature: $($signature.Substring(0, 16))..." -ForegroundColor White
    Write-Host ""
    
    try {
        $response = Invoke-WebRequest -Uri $webhookUrl -Method Post -Body $payload -Headers $headers -TimeoutSec 30
        
        Write-Success "Webhook delivered successfully: HTTP $($response.StatusCode)"
        Write-Info "Response: $($response.Content)"
        
        # Parse response
        try {
            $responseData = $response.Content | ConvertFrom-Json
            if ($responseData.status -eq "success") {
                Write-Success "WHMCS processed payment successfully"
                Write-Info "Message: $($responseData.message)"
                
                if ($responseData.message -match "already paid") {
                    Write-Info "Invoice was already paid"
                } else {
                    Write-Success "Invoice payment processed and status updated to PAID"
                }
            } else {
                Write-Error "WHMCS processing issue: $($responseData.message)"
            }
        } catch {
            if ($response.Content -match "success" -or $response.StatusCode -eq 200) {
                Write-Success "Payment processed successfully"
            }
        }
        
        return @{
            Success = $true
            StatusCode = $response.StatusCode
            Content = $response.Content
            TransactionId = $transactionId
            Reference = $reference
            WHMCSInvoiceId = $InvoiceId
            WHMCSClientId = $ClientId
        }
    }
    catch {
        Write-Error "Webhook delivery failed: $($_.Exception.Message)"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Main execution
Write-Host ""
Write-Host "LAHZA PAYMENT TEST WITH CLEAR WHMCS IDS" -ForegroundColor Magenta
Write-Host "=" * 45 -ForegroundColor Magenta
Write-Host ""

Write-Info "=== WHMCS CONFIGURATION ==="
Write-Host "   Domain: $Domain" -ForegroundColor White
Write-Host "   WHMCS Invoice ID: $InvoiceId (Real Number)" -ForegroundColor Green
Write-Host "   WHMCS Client ID: $ClientId (Real Number)" -ForegroundColor Green
Write-Host "   Payment Amount: $Amount USD" -ForegroundColor White
Write-Host "   Secret Key: $($SecretKey.Substring(0, 12))..." -ForegroundColor White
Write-Host ""

Write-Info "=== CUSTOMER INFORMATION ==="
Write-Host "   Name: $($CustomerData.firstname) $($CustomerData.lastname)" -ForegroundColor White
Write-Host "   Email: $($CustomerData.email)" -ForegroundColor White
Write-Host "   Phone: $($CustomerData.phonenumber)" -ForegroundColor White
Write-Host "   Company: $($CustomerData.companyname)" -ForegroundColor White
Write-Host "   Address: $($CustomerData.address1), $($CustomerData.address2)" -ForegroundColor White
Write-Host "   City/State: $($CustomerData.city), $($CustomerData.state)" -ForegroundColor White
Write-Host "   Country: $($CustomerData.country), Postcode: $($CustomerData.postcode)" -ForegroundColor White
Write-Host ""

# Process payment
Write-Host "PROCESSING PAYMENT WITH CLEAR IDS" -ForegroundColor Yellow
Write-Host "-" * 35 -ForegroundColor Yellow

$webhookResult = Send-ClearIDWebhook -Domain $Domain -InvoiceId $InvoiceId -ClientId $ClientId -Amount $Amount -CustomerData $CustomerData -SecretKey $SecretKey

Write-Host ""

# Final Summary
Write-Host "=== FINAL SUMMARY ===" -ForegroundColor Cyan
Write-Host ""

if ($webhookResult.Success) {
    Write-Host "✅ PAYMENT SUCCESSFUL!" -ForegroundColor Green
    Write-Host ""
    Write-Host "WHMCS Information:" -ForegroundColor Yellow
    Write-Host "   Invoice ID: $($webhookResult.WHMCSInvoiceId) (Real WHMCS Number)" -ForegroundColor Green
    Write-Host "   Client ID: $($webhookResult.WHMCSClientId) (Real WHMCS Number)" -ForegroundColor Green
    Write-Host ""
    Write-Host "Lahza Information:" -ForegroundColor Yellow
    Write-Host "   Transaction ID: $($webhookResult.TransactionId)" -ForegroundColor White
    Write-Host "   Payment Reference: $($webhookResult.Reference)" -ForegroundColor White
    Write-Host ""
    Write-Host "Processing Status:" -ForegroundColor Yellow
    Write-Host "   Webhook Status: SUCCESS (HTTP $($webhookResult.StatusCode))" -ForegroundColor Green
    Write-Host "   Customer Data: FULLY TRANSMITTED" -ForegroundColor Green
    Write-Host "   Invoice Status: UPDATED TO PAID" -ForegroundColor Green
} else {
    Write-Host "❌ PAYMENT FAILED" -ForegroundColor Red
    Write-Host "Error: $($webhookResult.Error)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== VERIFICATION LINKS ===" -ForegroundColor Cyan
Write-Host "   Invoice: http://$Domain/admin/invoices.php?action=edit&id=$InvoiceId" -ForegroundColor Gray
Write-Host "   Client: http://$Domain/admin/clientssummary.php?userid=$ClientId" -ForegroundColor Gray
Write-Host "   Gateway Logs: http://$Domain/admin/logs.php?type=gateway" -ForegroundColor Gray

Write-Host ""
Write-Host "Test completed at $(Get-Date)" -ForegroundColor Blue

# XAMPP Lahza Gateway Diagnostic
# Comprehensive diagnostic for local XAMPP environment
# Usage: .\xampp-diagnostic.ps1

Write-Host "🔍 XAMPP Lahza Gateway Diagnostic" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

# Test 1: XAMPP Installation Check
Write-Host "1. Checking XAMPP Installation..." -ForegroundColor Yellow
if (Test-Path "C:\xampp") {
    Write-Host "   ✅ XAMPP found at C:\xampp" -ForegroundColor Green
    
    # Check Apache
    $apacheProcess = Get-Process | Where-Object {$_.ProcessName -like "*httpd*" -or $_.ProcessName -like "*apache*"}
    if ($apacheProcess) {
        Write-Host "   ✅ Apache is running" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Apache is not running" -ForegroundColor Red
    }
    
    # Check MySQL
    $mysqlProcess = Get-Process | Where-Object {$_.ProcessName -like "*mysqld*"}
    if ($mysqlProcess) {
        Write-Host "   ✅ MySQL is running" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  MySQL may not be running" -ForegroundColor Yellow
    }
} else {
    Write-Host "   ❌ XAMPP not found in default location" -ForegroundColor Red
}

Write-Host ""

# Test 2: Port 80 Check
Write-Host "2. Checking Apache Port 80..." -ForegroundColor Yellow
$port80 = netstat -an | Select-String ":80.*LISTENING"
if ($port80) {
    Write-Host "   ✅ Port 80 is listening" -ForegroundColor Green
} else {
    Write-Host "   ❌ Port 80 is not listening" -ForegroundColor Red
}

Write-Host ""

# Test 3: WHMCS Accessibility
Write-Host "3. Testing WHMCS Accessibility..." -ForegroundColor Yellow
try {
    $whmcsResponse = Invoke-WebRequest "http://localhost/Whmcs/" -Method Head -TimeoutSec 5
    Write-Host "   ✅ WHMCS accessible: HTTP $($whmcsResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "   ❌ WHMCS not accessible: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 4: Lahza Gateway Files
Write-Host "4. Checking Lahza Gateway Files..." -ForegroundColor Yellow

$lahzaFiles = @(
    "C:\xampp\htdocs\Whmcs\modules\gateways\lahza.php",
    "C:\xampp\htdocs\Whmcs\modules\gateways\callback\lahza.php",
    "C:\xampp\htdocs\Whmcs\modules\gateways\lahza\tools.html"
)

foreach ($file in $lahzaFiles) {
    $fileName = Split-Path $file -Leaf
    if (Test-Path $file) {
        Write-Host "   ✅ $fileName exists" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $fileName missing" -ForegroundColor Red
    }
}

Write-Host ""

# Test 5: Webhook Callback Test
Write-Host "5. Testing Webhook Callback..." -ForegroundColor Yellow
try {
    $callbackResponse = Invoke-WebRequest "http://localhost/Whmcs/modules/gateways/callback/lahza.php" -Method Head -TimeoutSec 5
    Write-Host "   ✅ Webhook callback accessible: HTTP $($callbackResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Webhook callback not accessible: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 6: Webhook POST Test
Write-Host "6. Testing Webhook POST..." -ForegroundColor Yellow

$testPayload = @{
    event = "charge.success"
    data = @{
        id = "TEST_XAMPP_$(Get-Date -Format 'yyyyMMddHHmmss')"
        status = "success"
        amount = 5000
        currency = "USD"
        reference = "INV-10-TEST-$(Get-Date -Format 'yyyyMMddHHmmss')"
        metadata = @{
            invoiceid = "10"
            clientid = "1"
            description = "XAMPP Test Payment"
        }
    }
} | ConvertTo-Json -Depth 3

try {
    $headers = @{
        'Content-Type' = 'application/json'
        'X-Lahza-Signature' = 'test_signature_xampp'
        'User-Agent' = 'XAMPP-PowerShell-Test/1.0'
    }
    
    $webhookResponse = Invoke-WebRequest -Uri "http://localhost/Whmcs/modules/gateways/callback/lahza.php" -Method Post -Body $testPayload -Headers $headers -TimeoutSec 10
    
    Write-Host "   ✅ Webhook POST successful: HTTP $($webhookResponse.StatusCode)" -ForegroundColor Green
    Write-Host "   📄 Response preview:" -ForegroundColor White
    $responsePreview = $webhookResponse.Content.Substring(0, [Math]::Min(200, $webhookResponse.Content.Length))
    Write-Host "   $responsePreview..." -ForegroundColor Gray
    
} catch {
    Write-Host "   ❌ Webhook POST failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 7: PHP Configuration
Write-Host "7. Checking PHP Configuration..." -ForegroundColor Yellow
try {
    $phpInfo = php -v 2>$null
    if ($phpInfo) {
        $phpVersion = ($phpInfo -split "`n")[0]
        Write-Host "   ✅ PHP available: $phpVersion" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  PHP command not in PATH" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ⚠️  Could not check PHP version" -ForegroundColor Yellow
}

Write-Host ""

# Test 8: cURL Extension
Write-Host "8. Testing cURL Extension..." -ForegroundColor Yellow
try {
    $curlTest = php -m | Select-String "curl"
    if ($curlTest) {
        Write-Host "   ✅ cURL extension is loaded" -ForegroundColor Green
    } else {
        Write-Host "   ❌ cURL extension not found" -ForegroundColor Red
    }
} catch {
    Write-Host "   ⚠️  Could not check PHP extensions" -ForegroundColor Yellow
}

Write-Host ""

# Summary
Write-Host "📊 DIAGNOSTIC SUMMARY" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan
Write-Host ""

Write-Host "🔗 Quick Access Links:" -ForegroundColor White
Write-Host "   • WHMCS: http://localhost/Whmcs/" -ForegroundColor Gray
Write-Host "   • Lahza Tools: http://localhost/Whmcs/modules/gateways/lahza/tools.html" -ForegroundColor Gray
Write-Host "   • XAMPP Test: http://localhost/Whmcs/modules/gateways/lahza/xampp_test.php" -ForegroundColor Gray
Write-Host "   • Webhook Debug: http://localhost/Whmcs/modules/gateways/lahza/debug_webhook.php?debug_key=lahza_debug_2025" -ForegroundColor Gray

Write-Host ""
Write-Host "💡 Common XAMPP Issues:" -ForegroundColor White
Write-Host "   • Port 80 blocked - Stop IIS or change Apache port" -ForegroundColor Gray
Write-Host "   • Apache not starting - Check error logs in C:\xampp\apache\logs\" -ForegroundColor Gray
Write-Host "   • PHP errors - Check C:\xampp\php\php.ini configuration" -ForegroundColor Gray
Write-Host "   • Webhook not working - Check WHMCS gateway configuration" -ForegroundColor Gray

Write-Host ""
Write-Host "🎯 Next Steps for Invoice Update Issue:" -ForegroundColor White
Write-Host "   1. Open: http://localhost/Whmcs/modules/gateways/lahza/xampp_test.php" -ForegroundColor Gray
Write-Host "   2. Check the webhook test results" -ForegroundColor Gray
Write-Host "   3. If successful, check WHMCS admin for invoice updates" -ForegroundColor Gray
Write-Host "   4. If failed, check error messages and fix configuration" -ForegroundColor Gray

Write-Host ""
Write-Host "Diagnostic completed at $(Get-Date)" -ForegroundColor Blue

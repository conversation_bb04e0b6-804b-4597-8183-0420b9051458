<?php
/**
 * XAMPP Local Webhook Test for Lahza Gateway
 * Test webhook functionality in local development environment
 */

echo "<h1>🧪 XAMPP Lahza Webhook Test</h1>";
echo "<hr>";

// Test webhook URL
$webhookUrl = 'http://localhost/Whmcs/modules/gateways/callback/lahza.php';

// Test payload
$testPayload = [
    'event' => 'charge.success',
    'data' => [
        'id' => 'TEST_XAMPP_' . time(),
        'status' => 'success',
        'amount' => 5000, // $50.00 in cents
        'currency' => 'USD',
        'reference' => 'INV-10-TEST-' . time(),
        'gateway_response' => 'Test payment successful',
        'paid_at' => date('c'),
        'created_at' => date('c'),
        'channel' => 'card',
        'fees' => 150, // $1.50 in cents
        'metadata' => [
            'invoiceid' => '10', // Change this to your test invoice ID
            'clientid' => '1',
            'description' => 'XAMPP Test Payment',
            'company' => 'Test Company'
        ],
        'customer' => [
            'id' => 1,
            'email' => '<EMAIL>',
            'phone' => '+1234567890'
        ]
    ]
];

$payload = json_encode($testPayload, JSON_PRETTY_PRINT);

echo "<h2>📦 Test Payload:</h2>";
echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
echo htmlspecialchars($payload);
echo "</pre>";

echo "<h2>🎯 Webhook URL:</h2>";
echo "<p><code>$webhookUrl</code></p>";

// Test webhook
echo "<h2>🚀 Sending Webhook...</h2>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $webhookUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'X-Lahza-Signature: test_signature_xampp',
    'User-Agent: XAMPP-Test/1.0'
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<h2>📊 Results:</h2>";

if ($error) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>❌ cURL Error:</strong> " . htmlspecialchars($error);
    echo "</div>";
} else {
    $statusColor = ($httpCode == 200) ? '#d4edda' : '#fff3cd';
    $statusTextColor = ($httpCode == 200) ? '#155724' : '#856404';
    $statusIcon = ($httpCode == 200) ? '✅' : '⚠️';
    
    echo "<div style='background: $statusColor; color: $statusTextColor; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>$statusIcon HTTP Status:</strong> $httpCode";
    echo "</div>";
    
    echo "<h3>📄 Response:</h3>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
}

// Check if WHMCS is accessible
echo "<h2>🔍 WHMCS Accessibility Check:</h2>";

$whmcsUrl = 'http://localhost/Whmcs/';
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $whmcsUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$whmcsResponse = curl_exec($ch);
$whmcsHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($whmcsHttpCode == 200) {
    echo "<div style='background: #d4edda; color: #155724; padding: 10px; border-radius: 5px;'>";
    echo "✅ WHMCS is accessible at: <code>$whmcsUrl</code>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px;'>";
    echo "❌ WHMCS not accessible. HTTP Status: $whmcsHttpCode";
    echo "</div>";
}

// Environment info
echo "<h2>🖥️ Environment Information:</h2>";
echo "<ul>";
echo "<li><strong>PHP Version:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>Server:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</li>";
echo "<li><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</li>";
echo "<li><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "<li><strong>cURL Available:</strong> " . (function_exists('curl_init') ? 'Yes' : 'No') . "</li>";
echo "</ul>";

// Next steps
echo "<h2>💡 Next Steps:</h2>";
echo "<ol>";
echo "<li>Check the webhook response above</li>";
echo "<li>If HTTP 200, check WHMCS Gateway Logs for processing details</li>";
echo "<li>If error, check the error message and fix configuration</li>";
echo "<li>Test with a real invoice ID by changing the 'invoiceid' in metadata</li>";
echo "<li>Check WHMCS admin panel to see if invoice status updated</li>";
echo "</ol>";

echo "<h2>🔗 Useful Links:</h2>";
echo "<ul>";
echo "<li><a href='tools.html' target='_blank'>Lahza Tools Dashboard</a></li>";
echo "<li><a href='debug_webhook.php?debug_key=lahza_debug_2025' target='_blank'>Webhook Debug Tool</a></li>";
echo "<li><a href='test_invoice_update.php?debug_key=lahza_debug_2025' target='_blank'>Invoice Update Test</a></li>";
echo "<li><a href='view_logs.php?debug_key=lahza_debug_2025' target='_blank'>View Logs</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
